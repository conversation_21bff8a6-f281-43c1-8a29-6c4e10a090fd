import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../../core/services/analytics_service.dart';

class BodyDoublingProvider extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  List<BodyDoublingRoom> _availableRooms = [];
  BodyDoublingRoom? _currentRoom;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<BodyDoublingRoom> get availableRooms => _availableRooms;
  BodyDoublingRoom? get currentRoom => _currentRoom;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInRoom => _currentRoom != null;

  Future<void> initialize() async {
    _setLoading(true);
    
    try {
      await _loadAvailableRooms();
      await _checkCurrentRoom();
    } catch (e) {
      _setError('Failed to initialize body doubling: $e');
    }
    
    _setLoading(false);
  }

  Future<void> _loadAvailableRooms() async {
    final snapshot = await _firestore
        .collection('bodyDoublingRooms')
        .where('isActive', isEqualTo: true)
        .where('participants', arrayContains: _auth.currentUser?.uid)
        .orderBy('createdAt', descending: true)
        .limit(20)
        .get();

    _availableRooms = snapshot.docs
        .map((doc) => BodyDoublingRoom.fromFirestore(doc))
        .toList();
    
    notifyListeners();
  }

  Future<void> _checkCurrentRoom() async {
    if (_auth.currentUser == null) return;

    final snapshot = await _firestore
        .collection('bodyDoublingRooms')
        .where('participants', arrayContains: _auth.currentUser!.uid)
        .where('isActive', isEqualTo: true)
        .limit(1)
        .get();

    if (snapshot.docs.isNotEmpty) {
      _currentRoom = BodyDoublingRoom.fromFirestore(snapshot.docs.first);
      _startRoomListener();
    }
  }

  Future<void> quickJoinRoom() async {
    _setLoading(true);
    
    try {
      // Find a room with available space
      final availableRoom = _availableRooms.firstWhere(
        (room) => room.participants.length < room.maxParticipants,
        orElse: () => throw Exception('No available rooms'),
      );
      
      await joinRoom(availableRoom.id);
    } catch (e) {
      // Create a new room if no available rooms
      await createRoom('Quick Focus Session', 'Join others for focused work');
    }
    
    _setLoading(false);
  }

  Future<void> createRoom(String name, String description) async {
    if (_auth.currentUser == null) return;
    
    _setLoading(true);
    
    try {
      final roomData = {
        'id': '',
        'name': name,
        'description': description,
        'createdBy': _auth.currentUser!.uid,
        'createdAt': FieldValue.serverTimestamp(),
        'isActive': true,
        'maxParticipants': 8,
        'participants': [_auth.currentUser!.uid],
        'participantData': {
          _auth.currentUser!.uid: {
            'displayName': _auth.currentUser!.displayName ?? 'Anonymous',
            'joinedAt': FieldValue.serverTimestamp(),
            'status': 'focusing',
            'currentTask': '',
          }
        },
      };

      final docRef = await _firestore.collection('bodyDoublingRooms').add(roomData);
      
      // Update the document with its ID
      await docRef.update({'id': docRef.id});
      
      // Join the created room
      await _joinRoomById(docRef.id);
      
      await AnalyticsService.logBodyDoublingSessionJoined(
        roomId: docRef.id,
        participantCount: 1,
      );
      
    } catch (e) {
      _setError('Failed to create room: $e');
    }
    
    _setLoading(false);
  }

  Future<void> joinRoom(String roomId) async {
    await _joinRoomById(roomId);
  }

  Future<void> _joinRoomById(String roomId) async {
    if (_auth.currentUser == null) return;
    
    _setLoading(true);
    
    try {
      final roomRef = _firestore.collection('bodyDoublingRooms').doc(roomId);
      
      await _firestore.runTransaction((transaction) async {
        final roomDoc = await transaction.get(roomRef);
        
        if (!roomDoc.exists) {
          throw Exception('Room not found');
        }
        
        final roomData = roomDoc.data()!;
        final participants = List<String>.from(roomData['participants'] ?? []);
        final participantData = Map<String, dynamic>.from(roomData['participantData'] ?? {});
        
        if (participants.length >= (roomData['maxParticipants'] ?? 8)) {
          throw Exception('Room is full');
        }
        
        if (!participants.contains(_auth.currentUser!.uid)) {
          participants.add(_auth.currentUser!.uid);
          participantData[_auth.currentUser!.uid] = {
            'displayName': _auth.currentUser!.displayName ?? 'Anonymous',
            'joinedAt': FieldValue.serverTimestamp(),
            'status': 'focusing',
            'currentTask': '',
          };
          
          transaction.update(roomRef, {
            'participants': participants,
            'participantData': participantData,
          });
        }
      });
      
      // Load the updated room
      final updatedDoc = await roomRef.get();
      _currentRoom = BodyDoublingRoom.fromFirestore(updatedDoc);
      
      _startRoomListener();
      
      await AnalyticsService.logBodyDoublingSessionJoined(
        roomId: roomId,
        participantCount: _currentRoom!.participants.length,
      );
      
    } catch (e) {
      _setError('Failed to join room: $e');
    }
    
    _setLoading(false);
  }

  Future<void> leaveRoom() async {
    if (_currentRoom == null || _auth.currentUser == null) return;
    
    try {
      final roomRef = _firestore.collection('bodyDoublingRooms').doc(_currentRoom!.id);
      
      await _firestore.runTransaction((transaction) async {
        final roomDoc = await transaction.get(roomRef);
        
        if (roomDoc.exists) {
          final roomData = roomDoc.data()!;
          final participants = List<String>.from(roomData['participants'] ?? []);
          final participantData = Map<String, dynamic>.from(roomData['participantData'] ?? {});
          
          participants.remove(_auth.currentUser!.uid);
          participantData.remove(_auth.currentUser!.uid);
          
          if (participants.isEmpty) {
            // Delete room if no participants left
            transaction.update(roomRef, {'isActive': false});
          } else {
            transaction.update(roomRef, {
              'participants': participants,
              'participantData': participantData,
            });
          }
        }
      });
      
      _currentRoom = null;
      _stopRoomListener();
      
    } catch (e) {
      _setError('Failed to leave room: $e');
    }
    
    notifyListeners();
  }

  Future<void> updateStatus(String status, {String? currentTask}) async {
    if (_currentRoom == null || _auth.currentUser == null) return;
    
    try {
      await _firestore
          .collection('bodyDoublingRooms')
          .doc(_currentRoom!.id)
          .update({
        'participantData.${_auth.currentUser!.uid}.status': status,
        if (currentTask != null)
          'participantData.${_auth.currentUser!.uid}.currentTask': currentTask,
      });
    } catch (e) {
      debugPrint('Failed to update status: $e');
    }
  }

  void _startRoomListener() {
    if (_currentRoom == null) return;
    
    _firestore
        .collection('bodyDoublingRooms')
        .doc(_currentRoom!.id)
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists) {
        _currentRoom = BodyDoublingRoom.fromFirestore(snapshot);
        notifyListeners();
      }
    });
  }

  void _stopRoomListener() {
    // Room listener will automatically stop when the stream is no longer listened to
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}

class BodyDoublingRoom {
  final String id;
  final String name;
  final String description;
  final String createdBy;
  final DateTime createdAt;
  final bool isActive;
  final int maxParticipants;
  final List<BodyDoublingParticipant> participants;

  BodyDoublingRoom({
    required this.id,
    required this.name,
    required this.description,
    required this.createdBy,
    required this.createdAt,
    required this.isActive,
    required this.maxParticipants,
    required this.participants,
  });

  factory BodyDoublingRoom.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final participantIds = List<String>.from(data['participants'] ?? []);
    final participantData = Map<String, dynamic>.from(data['participantData'] ?? {});
    
    final participants = participantIds.map((id) {
      final userData = participantData[id] ?? {};
      return BodyDoublingParticipant(
        id: id,
        displayName: userData['displayName'] ?? 'Anonymous',
        status: userData['status'] ?? 'focusing',
        currentTask: userData['currentTask'] ?? '',
        joinedAt: userData['joinedAt'] != null 
            ? (userData['joinedAt'] as Timestamp).toDate()
            : DateTime.now(),
      );
    }).toList();
    
    return BodyDoublingRoom(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      createdBy: data['createdBy'] ?? '',
      createdAt: data['createdAt'] != null 
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      isActive: data['isActive'] ?? false,
      maxParticipants: data['maxParticipants'] ?? 8,
      participants: participants,
    );
  }
}

class BodyDoublingParticipant {
  final String id;
  final String displayName;
  final String status; // 'focusing', 'break', 'away'
  final String currentTask;
  final DateTime joinedAt;

  BodyDoublingParticipant({
    required this.id,
    required this.displayName,
    required this.status,
    required this.currentTask,
    required this.joinedAt,
  });
}
