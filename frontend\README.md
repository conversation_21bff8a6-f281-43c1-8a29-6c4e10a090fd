# FocusFlow - ADHD-Friendly Day Planner

A Flutter mobile app designed specifically for people with ADHD to manage their daily tasks with AI assistance, energy tracking, and gentle motivation.

## 🌟 Features

### Core ADHD-Friendly Features
- **Today's Three**: Focus on maximum 3 tasks per day to avoid overwhelm
- **Energy Tracking**: Match tasks to your energy levels throughout the day
- **AI Task Breakdown**: Intelligent task decomposition with ADHD-specific prompts
- **Gentle Nudges**: Non-intrusive reminders that respect your mental space
- **Dopamine Celebrations**: Satisfying animations and rewards for completed tasks
- **Focus Mode**: Pomodoro timer with ambient sounds and body doubling options

### Advanced Features
- **Offline Support**: Works without internet connection
- **Smart Caching**: Reduces AI costs by 80% through intelligent caching
- **Streak Tracking**: Build sustainable habits with gentle motivation
- **Weekly Reflection**: AI-powered insights into your productivity patterns
- **Adaptive Planning**: Task recommendations based on energy and time
- **Accessibility**: Dyslexia-friendly fonts and ADHD-optimized design

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (>=3.16.0)
- Dart SDK (>=3.2.0)
- Firebase project setup
- OpenAI API key (optional, for AI features)

### Installation

1. **Clone the repository**
   \`\`\`bash
   git clone https://github.com/yourusername/focusflow.git
   cd focusflow/frontend
   \`\`\`

2. **Install dependencies**
   \`\`\`bash
   flutter pub get
   \`\`\`

3. **Firebase Setup**
   - Create a new Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Enable Authentication, Firestore, Analytics, and Crashlytics
   - Download configuration files:
     - `google-services.json` for Android (place in `android/app/`)
     - `GoogleService-Info.plist` for iOS (place in `ios/Runner/`)
   - Update `lib/app/firebase_options.dart` with your project configuration

4. **Configure Firebase Security Rules**
   \`\`\`javascript
   // Firestore Rules
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /users/{userId} {
         allow read, write: if request.auth != null && request.auth.uid == userId;
       }
       match /tasks/{taskId} {
         allow read, write: if request.auth != null && 
           resource.data.userId == request.auth.uid;
       }
       match /ai_cache/{document} {
         allow read, write: if request.auth != null;
       }
     }
   }
   \`\`\`

5. **Set up environment variables** (Optional)
   Create a `.env` file in the root directory:
   \`\`\`
   OPENAI_API_KEY=your_openai_api_key_here
   \`\`\`

6. **Run the app**
   \`\`\`bash
   flutter run
   \`\`\`

## 🏗️ Architecture

### Project Structure
\`\`\`
lib/
├── app/                    # App configuration and routing
├── core/                   # Core business logic
│   ├── models/            # Data models
│   ├── services/          # Business services
│   └── theme/             # App theming
├── features/              # Feature-based modules
│   ├── auth/              # Authentication
│   ├── today/             # Daily task management
│   ├── focus/             # Focus modes
│   ├── tasks/             # Task management
│   ├── reflection/        # Weekly reflection
│   └── profile/           # User profile
└── shared/                # Shared components
    ├── providers/         # State management
    └── widgets/           # Reusable widgets
\`\`\`

### State Management
- **Provider Pattern**: For app-wide state management
- **Local State**: For component-specific state
- **Firebase**: For data persistence and real-time updates

### Design Principles
- **ADHD-First Design**: Every UI decision considers ADHD needs
- **Gentle Interactions**: No aggressive notifications or overwhelming interfaces
- **Accessibility**: Support for dyslexia, color blindness, and motor difficulties
- **Performance**: Smooth animations and responsive interactions

## 🎨 ADHD-Friendly Design

### Color System
- **Primary**: Calming blue (#4A90E2) for focus and trust
- **Secondary**: Gentle teal (#50C878) for success and growth
- **Accent**: Warm orange (#FF8C42) for energy and motivation
- **Energy Colors**: Visual coding for different energy levels

### Typography
- **Inter Font**: Clean, readable, and ADHD-friendly
- **OpenDyslexic**: Optional dyslexia-friendly font
- **Large Text**: Minimum 14px for body text
- **High Contrast**: WCAG AA compliant contrast ratios

### Interactions
- **Gentle Haptics**: Light feedback for actions
- **Smooth Animations**: 200-500ms duration for state changes
- **Clear Visual Hierarchy**: Obvious primary and secondary actions
- **Generous Spacing**: Minimum 16px between interactive elements

## 🔧 Configuration

### Theme Customization
Modify `lib/core/theme/app_theme.dart` to customize:
- Color schemes (light/dark/pastel/dyslexia modes)
- Typography scales
- Component styling
- Animation durations

### AI Integration
Configure AI features in `lib/core/services/ai_service.dart`:
- OpenAI model selection
- Caching strategies
- ADHD-specific prompts
- Cost optimization

### Notifications
Customize notification behavior in `lib/core/services/notification_service.dart`:
- Gentle reminder timing
- Sound selection
- Vibration patterns
- Frequency limits

## 📱 Building for Production

### Android
1. **Configure signing**
   - Create `android/key.properties`
   - Generate signing key: `keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload`

2. **Build release APK**
   \`\`\`bash
   flutter build apk --release
   \`\`\`

3. **Build App Bundle**
   \`\`\`bash
   flutter build appbundle --release
   \`\`\`

### iOS
1. **Configure signing in Xcode**
   - Open `ios/Runner.xcworkspace`
   - Configure team and bundle identifier

2. **Build for App Store**
   \`\`\`bash
   flutter build ios --release
   \`\`\`

## 🧪 Testing

### Unit Tests
\`\`\`bash
flutter test
\`\`\`

### Integration Tests
\`\`\`bash
flutter test integration_test/
\`\`\`

### Widget Tests
\`\`\`bash
flutter test test/widget_test.dart
\`\`\`

## 📊 Analytics & Monitoring

### Firebase Analytics Events
- `task_completed`: Track task completion patterns
- `energy_level_updated`: Monitor energy tracking usage
- `ai_breakdown_generated`: Measure AI feature adoption
- `focus_session_started`: Track focus mode usage

### Crashlytics
- Automatic crash reporting
- Custom error logging
- Performance monitoring
- User feedback collection

## 🔒 Privacy & Security

### Data Protection
- End-to-end encryption for sensitive data
- Local storage for offline functionality
- GDPR compliant data handling
- User-controlled data deletion

### Security Features
- Firebase Authentication
- Firestore security rules
- Input validation and sanitization
- Rate limiting for API calls

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines
- Follow Flutter/Dart style guide
- Write tests for new features
- Update documentation
- Consider ADHD accessibility in all changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- ADHD community for feedback and insights
- Flutter team for the amazing framework
- Firebase for backend infrastructure
- OpenAI for AI capabilities

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/yourusername/focusflow/issues)
- 💬 Community: [Discord Server](https://discord.gg/focusflow)

---

**Made with ❤️ for the ADHD community**
