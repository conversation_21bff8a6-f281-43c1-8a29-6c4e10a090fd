import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

class FirebaseService {
  static FirebaseAuth get auth => FirebaseAuth.instance;
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;
  static FirebaseAnalytics get analytics => FirebaseAnalytics.instance;
  static FirebaseCrashlytics get crashlytics => FirebaseCrashlytics.instance;
  static FirebaseMessaging get messaging => FirebaseMessaging.instance;

  static Future<void> initialize() async {
    try {
      // Initialize Firebase
      await Firebase.initializeApp();
      
      // Configure Crashlytics
      if (!kDebugMode) {
        FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;
        PlatformDispatcher.instance.onError = (error, stack) {
          FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
          return true;
        };
      }
      
      // Configure Firestore settings
      firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );
      
      // Enable offline persistence
      await firestore.enableNetwork();
      
      // Configure FCM
      await _configureFCM();
      
      print('Firebase services initialized successfully');
    } catch (e) {
      print('Firebase initialization error: $e');
      rethrow;
    }
  }
  
  static Future<void> _configureFCM() async {
    try {
      // Request permission for notifications
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
      
      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('User granted permission for notifications');
        
        // Get FCM token
        String? token = await messaging.getToken();
        print('FCM Token: $token');
        
        // Listen for token refresh
        messaging.onTokenRefresh.listen((newToken) {
          print('FCM Token refreshed: $newToken');
          // Update token in Firestore if user is logged in
          _updateFCMToken(newToken);
        });
      }
    } catch (e) {
      print('FCM configuration error: $e');
    }
  }
  
  static Future<void> _updateFCMToken(String token) async {
    try {
      final user = auth.currentUser;
      if (user != null) {
        await firestore.collection('users').doc(user.uid).update({
          'fcmToken': token,
          'lastTokenUpdate': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      print('FCM token update error: $e');
    }
  }
  
  // Analytics helper methods
  static Future<void> logEvent(String name, Map<String, Object>? parameters) async {
    try {
      await analytics.logEvent(name: name, parameters: parameters);
    } catch (e) {
      print('Analytics event error: $e');
    }
  }
  
  static Future<void> setUserProperties(Map<String, String> properties) async {
    try {
      for (final entry in properties.entries) {
        await analytics.setUserProperty(name: entry.key, value: entry.value);
      }
    } catch (e) {
      print('Analytics user properties error: $e');
    }
  }
  
  // Crashlytics helper methods
  static Future<void> recordError(dynamic exception, StackTrace? stack, {bool fatal = false}) async {
    try {
      await crashlytics.recordError(exception, stack, fatal: fatal);
    } catch (e) {
      print('Crashlytics record error: $e');
    }
  }
  
  static Future<void> log(String message) async {
    try {
      await crashlytics.log(message);
    } catch (e) {
      print('Crashlytics log error: $e');
    }
  }
}
