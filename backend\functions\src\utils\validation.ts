import type { Task, User, DailyPlan } from "../models/types"

export class ValidationUtils {
  static validateTask(task: Partial<Task>): string[] {
    const errors: string[] = []

    if (!task.title || task.title.trim().length === 0) {
      errors.push("Task title is required")
    }

    if (task.title && task.title.length > 200) {
      errors.push("Task title must be less than 200 characters")
    }

    if (task.description && task.description.length > 1000) {
      errors.push("Task description must be less than 1000 characters")
    }

    if (task.priority && ![1, 2, 3].includes(task.priority)) {
      errors.push("Priority must be 1, 2, or 3")
    }

    if (task.estimatedMinutes && (task.estimatedMinutes < 1 || task.estimatedMinutes > 480)) {
      errors.push("Estimated minutes must be between 1 and 480 (8 hours)")
    }

    return errors
  }

  static validateUser(user: Partial<User>): string[] {
    const errors: string[] = []

    if (!user.email || !this.isValidEmail(user.email)) {
      errors.push("Valid email is required")
    }

    if (!user.displayName || user.displayName.trim().length === 0) {
      errors.push("Display name is required")
    }

    if (user.displayName && user.displayName.length > 50) {
      errors.push("Display name must be less than 50 characters")
    }

    return errors
  }

  static validateDailyPlan(plan: Partial<DailyPlan>): string[] {
    const errors: string[] = []

    if (!plan.date || !this.isValidDate(plan.date)) {
      errors.push("Valid date is required (YYYY-MM-DD format)")
    }

    if (plan.energyLevel && ![1, 2, 3, 4, 5].includes(plan.energyLevel)) {
      errors.push("Energy level must be between 1 and 5")
    }

    if (plan.todaysThree && plan.todaysThree.length > 3) {
      errors.push("Today's Three can contain maximum 3 tasks")
    }

    return errors
  }

  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  private static isValidDate(date: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/
    if (!dateRegex.test(date)) return false

    const parsedDate = new Date(date)
    return parsedDate.toISOString().split("T")[0] === date
  }
}
