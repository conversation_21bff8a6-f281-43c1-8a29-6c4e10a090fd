import type { FirebaseFirestore } from "firebase-admin/firestore"

// Core TypeScript interfaces for FocusFlow

export interface User {
  id: string
  email: string
  displayName: string
  createdAt: FirebaseFirestore.Timestamp
  subscription: {
    tier: "free" | "pro"
    expiresAt: FirebaseFirestore.Timestamp | null
    provider: "revenueCat"
    originalTransactionId?: string
  }
  preferences: {
    theme: "light" | "dark" | "pastel" | "dyslexia"
    focusMode: "pomodoro" | "custom" | "deep-work"
    notifications: boolean
    energyTracking: boolean
    soundEnabled: boolean
    defaultFocusMinutes: number
  }
  stats: {
    totalTasksCompleted: number
    currentStreak: number
    longestStreak: number
    totalFocusMinutes: number
    weeklyGoal: number
    lastActiveDate: string // YYYY-MM-DD
  }
  onboarding: {
    completed: boolean
    adhdAssessmentScore: number // 1-10
    primaryChallenges: string[] // ['focus', 'organization', 'time-management']
    goals: string[]
  }
}

export interface Task {
  id: string
  userId: string
  title: string
  description?: string
  priority: 1 | 2 | 3 // 1 = high, 2 = medium, 3 = low
  estimatedMinutes: number
  actualMinutes: number
  status: "todo" | "in-progress" | "completed" | "archived"
  aiBreakdown?: {
    steps: string[]
    difficulty: 1 | 2 | 3 | 4 | 5
    suggestedOrder: number[]
    timeEstimates: number[] // minutes per step
    tips: string[]
    cachedAt: FirebaseFirestore.Timestamp
    model: string
  }
  tags: string[]
  dueDate?: FirebaseFirestore.Timestamp
  createdAt: FirebaseFirestore.Timestamp
  completedAt?: FirebaseFirestore.Timestamp
  parentTaskId?: string // for subtasks
  isSubtask: boolean
}

export interface DailyPlan {
  id: string // YYYY-MM-DD format
  userId: string
  date: string // YYYY-MM-DD
  todaysThree: string[] // task IDs
  energyLevel: 1 | 2 | 3 | 4 | 5
  mood: "low" | "medium" | "high"
  completedTasks: number
  focusMinutes: number
  notes?: string
  aiSuggestions?: {
    taskOrder: string[]
    energyOptimized: boolean
    reasoning: string
    generatedAt: FirebaseFirestore.Timestamp
  }
  createdAt: FirebaseFirestore.Timestamp
  updatedAt: FirebaseFirestore.Timestamp
}

export interface FocusSession {
  id: string
  userId: string
  taskId: string
  duration: number // actual minutes completed
  plannedDuration: number // intended minutes
  type: "pomodoro" | "deep-work" | "body-doubling"
  startedAt: FirebaseFirestore.Timestamp
  completedAt?: FirebaseFirestore.Timestamp
  interruptions: number
  moodBefore: 1 | 2 | 3 | 4 | 5
  moodAfter?: 1 | 2 | 3 | 4 | 5
  notes?: string
  ambientSound?: string
  completed: boolean
}

export interface WeeklyReflection {
  id: string // YYYY-WW format
  userId: string
  weekStart: string // YYYY-MM-DD (Monday)
  weekEnd: string // YYYY-MM-DD (Sunday)
  wins: string[]
  challenges: string[]
  insights: string[]
  aiAnalysis?: {
    patterns: string[]
    suggestions: string[]
    encouragement: string
    nextWeekFocus: string[]
    generatedAt: FirebaseFirestore.Timestamp
  }
  goals: string[]
  createdAt: FirebaseFirestore.Timestamp
}

export interface AICache {
  id: string // hash of prompt + model
  prompt: string
  response: string
  model: string
  createdAt: FirebaseFirestore.Timestamp
  expiresAt: FirebaseFirestore.Timestamp
  hitCount: number
  lastUsed: FirebaseFirestore.Timestamp
  userId?: string // for user-specific caching
}

export interface BodyDoublingRoom {
  id: string
  name: string
  description: string
  maxParticipants: number
  currentParticipants: string[] // user IDs
  isActive: boolean
  createdBy: string
  createdAt: FirebaseFirestore.Timestamp
  sessionDuration: number // minutes
  focusType: "silent" | "ambient" | "music"
}
