import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'analytics_service.dart';

class AdService {
  static final AdService _instance = AdService._internal();
  factory AdService() => _instance;
  AdService._internal();

  final AnalyticsService _analytics = AnalyticsService();
  
  // Ad unit IDs (replace with your actual IDs)
  static const String _bannerAdUnitId = kDebugMode
      ? 'ca-app-pub-3940256099942544/6300978111' // Test ID
      : 'ca-app-pub-YOUR_ACTUAL_ID/banner';
      
  static const String _interstitialAdUnitId = kDebugMode
      ? 'ca-app-pub-3940256099942544/1033173712' // Test ID
      : 'ca-app-pub-YOUR_ACTUAL_ID/interstitial';

  // Ad frequency limits (ADHD-friendly)
  static const int _maxInterstitialAdsPerDay = 2;
  static const int _minTimeBetweenInterstitials = 30; // minutes

  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  bool _isInterstitialReady = false;

  // Initialize ads
  Future<void> initialize() async {
    await MobileAds.instance.initialize();
    
    // Set request configuration for ADHD-friendly ads
    final requestConfiguration = RequestConfiguration(
      tagForChildDirectedTreatment: TagForChildDirectedTreatment.no,
      tagForUnderAgeOfConsent: TagForUnderAgeOfConsent.no,
      maxAdContentRating: MaxAdContentRating.g, // Family-friendly content only
    );
    
    MobileAds.instance.updateRequestConfiguration(requestConfiguration);
  }

  // Create banner ad
  BannerAd createBannerAd({
    required String placement,
    required Function(Ad) onAdLoaded,
    required Function(Ad, LoadAdError) onAdFailedToLoad,
  }) {
    return BannerAd(
      adUnitId: _bannerAdUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          _analytics.trackAdShown('banner', placement);
          onAdLoaded(ad);
        },
        onAdFailedToLoad: (ad, error) {
          debugPrint('Banner ad failed to load: $error');
          onAdFailedToLoad(ad, error);
        },
        onAdClicked: (ad) {
          _analytics.trackAdClicked('banner', placement);
        },
      ),
    );
  }

  // Load interstitial ad
  Future<void> loadInterstitialAd() async {
    await InterstitialAd.load(
      adUnitId: _interstitialAdUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isInterstitialReady = true;
          
          _interstitialAd!.setImmersiveMode(true);
          _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
            onAdShowedFullScreenContent: (ad) {
              _analytics.trackAdShown('interstitial', 'full_screen');
            },
            onAdDismissedFullScreenContent: (ad) {
              ad.dispose();
              _interstitialAd = null;
              _isInterstitialReady = false;
              // Preload next ad
              loadInterstitialAd();
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              debugPrint('Interstitial ad failed to show: $error');
              ad.dispose();
              _interstitialAd = null;
              _isInterstitialReady = false;
            },
            onAdClicked: (ad) {
              _analytics.trackAdClicked('interstitial', 'full_screen');
            },
          );
        },
        onAdFailedToLoad: (error) {
          debugPrint('Interstitial ad failed to load: $error');
          _isInterstitialReady = false;
        },
      ),
    );
  }

  // Show interstitial ad with ADHD-friendly frequency limits
  Future<bool> showInterstitialAd({required String placement}) async {
    if (!_isInterstitialReady || _interstitialAd == null) {
      return false;
    }

    // Check frequency limits
    final canShow = await _canShowInterstitialAd();
    if (!canShow) {
      debugPrint('Interstitial ad blocked by frequency limits');
      return false;
    }

    try {
      await _interstitialAd!.show();
      await _recordInterstitialAdShown();
      return true;
    } catch (e) {
      debugPrint('Failed to show interstitial ad: $e');
      return false;
    }
  }

  // Check if we can show interstitial ad (respects ADHD-friendly limits)
  Future<bool> _canShowInterstitialAd() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    
    // Check daily limit
    final todayCount = prefs.getInt('interstitial_count_$today') ?? 0;
    if (todayCount >= _maxInterstitialAdsPerDay) {
      return false;
    }
    
    // Check time between ads
    final lastShown = prefs.getInt('last_interstitial_time') ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    final timeSinceLastAd = (now - lastShown) / (1000 * 60); // minutes
    
    if (timeSinceLastAd < _minTimeBetweenInterstitials) {
      return false;
    }
    
    return true;
  }

  // Record that an interstitial ad was shown
  Future<void> _recordInterstitialAdShown() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    final now = DateTime.now().millisecondsSinceEpoch;
    
    // Update daily count
    final todayCount = prefs.getInt('interstitial_count_$today') ?? 0;
    await prefs.setInt('interstitial_count_$today', todayCount + 1);
    
    // Update last shown time
    await prefs.setInt('last_interstitial_time', now);
  }

  // Check if user should see ads (not pro subscriber)
  bool shouldShowAds(bool isPro) {
    return !isPro;
  }

  // Dispose of ads
  void dispose() {
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
  }
}
