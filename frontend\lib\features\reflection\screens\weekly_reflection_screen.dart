import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:lottie/lottie.dart';

import '../../../shared/widgets/calm_card.dart';
import '../../../shared/widgets/adhd_button.dart';
import '../../../shared/providers/subscription_provider.dart';
import '../providers/reflection_provider.dart';
import '../widgets/weekly_stats_widget.dart';
import '../widgets/ai_insights_widget.dart';
import '../widgets/progress_chart_widget.dart';

class WeeklyReflectionScreen extends StatefulWidget {
  const WeeklyReflectionScreen({super.key});

  @override
  State<WeeklyReflectionScreen> createState() => _WeeklyReflectionScreenState();
}

class _WeeklyReflectionScreenState extends State<WeeklyReflectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));
    
    _fadeController.forward();
    
    // Load reflection data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ReflectionProvider>().loadWeeklyReflection();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final subscriptionProvider = context.watch<SubscriptionProvider>();
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        title: const Text('Weekly Reflection'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareReflection(context),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<ReflectionProvider>(
          builder: (context, reflectionProvider, child) {
            // Check if user has access to weekly reflection
            if (!subscriptionProvider.canUseFeature(ProFeature.weeklyReflection)) {
              return _buildUpgradePrompt(context);
            }

            if (reflectionProvider.isLoading) {
              return _buildLoadingState(context);
            }

            if (reflectionProvider.weeklyReflection == null) {
              return _buildGeneratePrompt(context, reflectionProvider);
            }

            return _buildReflectionContent(context, reflectionProvider);
          },
        ),
      ),
    );
  }

  Widget _buildUpgradePrompt(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Lottie.asset(
              'assets/lottie/reflection_upgrade.json',
              width: 200,
              height: 200,
            ),
            const SizedBox(height: 24),
            Text(
              'Weekly AI Reflection',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Get personalized insights about your productivity patterns, wins, and areas for improvement.',
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ADHDButton(
              text: 'Upgrade to Pro',
              icon: Icons.star,
              type: ADHDButtonType.primary,
              onPressed: () {
                context.read<SubscriptionProvider>().trackPaywallView();
                Navigator.pushNamed(context, '/subscription');
              },
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Maybe Later'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Lottie.asset(
            'assets/lottie/ai_thinking.json',
            width: 150,
            height: 150,
          ),
          const SizedBox(height: 24),
          Text(
            'Analyzing your week...',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'AI is reviewing your patterns and progress',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneratePrompt(BuildContext context, ReflectionProvider provider) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Lottie.asset(
              'assets/lottie/weekly_reflection.json',
              width: 200,
              height: 200,
            ),
            const SizedBox(height: 24),
            Text(
              'Ready for Your Weekly Reflection?',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Let AI analyze your week and provide personalized insights to help you improve.',
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ADHDButton(
              text: 'Generate Reflection',
              icon: Icons.psychology,
              type: ADHDButtonType.primary,
              onPressed: () => provider.generateWeeklyReflection(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReflectionContent(BuildContext context, ReflectionProvider provider) {
    final reflection = provider.weeklyReflection!;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with week info
          CalmCard(
            backgroundColor: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.insights,
                      color: Theme.of(context).colorScheme.primary,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Week of ${_formatDate(reflection.weekStart)}',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Your ADHD journey insights',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          
          // Weekly stats
          WeeklyStatsWidget(reflection: reflection),
          const SizedBox(height: 24),
          
          // Progress chart
          ProgressChartWidget(reflection: reflection),
          const SizedBox(height: 24),
          
          // AI insights
          AIInsightsWidget(reflection: reflection),
          const SizedBox(height: 24),
          
          // Action items for next week
          _buildActionItems(context, reflection),
          const SizedBox(height: 24),
          
          // Regenerate button
          ADHDButton(
            text: 'Generate New Reflection',
            icon: Icons.refresh,
            type: ADHDButtonType.outline,
            onPressed: () => provider.generateWeeklyReflection(),
          ),
          const SizedBox(height: 100), // Bottom padding
        ],
      ),
    );
  }

  Widget _buildActionItems(BuildContext context, WeeklyReflection reflection) {
    final theme = Theme.of(context);
    
    return CalmCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: theme.colorScheme.secondary,
              ),
              const SizedBox(width: 8),
              Text(
                'Next Week\'s Focus',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...reflection.actionItems.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  margin: const EdgeInsets.only(top: 8, right: 12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.secondary,
                    shape: BoxShape.circle,
                  ),
                ),
                Expanded(
                  child: Text(
                    item,
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}';
  }

  void _shareReflection(BuildContext context) {
    // Implement sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sharing feature coming soon!'),
      ),
    );
  }
}
