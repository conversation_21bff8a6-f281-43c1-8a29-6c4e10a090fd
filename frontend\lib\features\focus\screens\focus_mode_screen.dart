import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';

import '../../../shared/widgets/calm_card.dart';
import '../../../shared/widgets/adhd_button.dart';
import '../widgets/pomodoro_timer.dart';
import '../widgets/ambient_sounds.dart';
import '../widgets/progress_ring.dart';
import '../providers/focus_provider.dart';

class FocusModeScreen extends StatefulWidget {
  const FocusModeScreen({super.key});

  @override
  State<FocusModeScreen> createState() => _FocusModeScreenState();
}

class _FocusModeScreenState extends State<FocusModeScreen>
    with TickerProviderStateMixin {
  late AnimationController _breathingController;
  late Animation<double> _breathingAnimation;

  @override
  void initState() {
    super.initState();
    _breathingController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    _breathingAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _breathingController,
      curve: Curves.easeInOut,
    ));
    
    // Start breathing animation
    _breathingController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _breathingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: SafeArea(
        child: Consumer<FocusProvider>(
          builder: (context, focusProvider, child) {
            return CustomScrollView(
              slivers: [
                _buildAppBar(context, focusProvider),
                SliverFillRemaining(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        const SizedBox(height: 32),
                        _buildTimerSection(context, focusProvider),
                        const SizedBox(height: 48),
                        _buildCurrentTask(context, focusProvider),
                        const SizedBox(height: 32),
                        _buildAmbientSounds(context, focusProvider),
                        const Spacer(),
                        _buildControlButtons(context, focusProvider),
                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context, FocusProvider provider) {
    return SliverAppBar(
      title: const Text('Focus Mode'),
      centerTitle: true,
      floating: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () => _showFocusSettings(context, provider),
        ),
      ],
    );
  }

  Widget _buildTimerSection(BuildContext context, FocusProvider provider) {
    return AnimatedBuilder(
      animation: _breathingAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: provider.isActive ? _breathingAnimation.value : 1.0,
          child: ProgressRing(
            progress: provider.progress,
            size: 280,
            strokeWidth: 12,
            backgroundColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            progressColor: _getTimerColor(context, provider),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _formatTime(provider.remainingSeconds),
                  style: Theme.of(context).textTheme.displayLarge?.copyWith(
                    fontWeight: FontWeight.w300,
                    color: _getTimerColor(context, provider),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _getTimerLabel(provider),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                if (provider.currentTask != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Step ${provider.currentStep + 1} of ${provider.totalSteps}',
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCurrentTask(BuildContext context, FocusProvider provider) {
    if (provider.currentTask == null) {
      return CalmCard(
        child: Column(
          children: [
            Icon(
              Icons.task_alt,
              size: 48,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No task selected',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Choose a task from Today\'s Three to focus on',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),
            ADHDButton(
              text: 'Select Task',
              icon: Icons.list,
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      );
    }

    final task = provider.currentTask!;
    return CalmCard(
      backgroundColor: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.task,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  task.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          if (task.aiBreakdown != null && provider.currentStepDescription.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Current Step:',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    provider.currentStepDescription,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAmbientSounds(BuildContext context, FocusProvider provider) {
    return CalmCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ambient Sounds',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          AmbientSounds(
            currentSound: provider.currentAmbientSound,
            isPlaying: provider.isSoundPlaying,
            volume: provider.soundVolume,
            onSoundChanged: (sound) => provider.setAmbientSound(sound),
            onPlayPause: () => provider.toggleSound(),
            onVolumeChanged: (volume) => provider.setSoundVolume(volume),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons(BuildContext context, FocusProvider provider) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ADHDButton(
                text: provider.isActive ? 'Pause' : 'Start',
                icon: provider.isActive ? Icons.pause : Icons.play_arrow,
                type: ADHDButtonType.primary,
                size: ADHDButtonSize.large,
                onPressed: () => provider.toggleTimer(),
              ),
            ),
            const SizedBox(width: 16),
            ADHDButton(
              text: 'Reset',
              icon: Icons.refresh,
              type: ADHDButtonType.outline,
              size: ADHDButtonSize.large,
              onPressed: () => provider.resetTimer(),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: ADHDButton(
                text: 'Need a Break?',
                icon: Icons.coffee,
                type: ADHDButtonType.text,
                onPressed: () => _showBreakDialog(context, provider),
              ),
            ),
            Expanded(
              child: ADHDButton(
                text: 'Skip Step',
                icon: Icons.skip_next,
                type: ADHDButtonType.text,
                onPressed: provider.currentTask?.aiBreakdown != null 
                    ? () => provider.nextStep()
                    : null,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Color _getTimerColor(BuildContext context, FocusProvider provider) {
    if (provider.isBreakTime) {
      return const Color(0xFF28A745); // Green for break
    } else if (provider.remainingSeconds <= 300) { // Last 5 minutes
      return const Color(0xFFF5A623); // Orange for urgency
    } else {
      return Theme.of(context).colorScheme.primary;
    }
  }

  String _getTimerLabel(FocusProvider provider) {
    if (provider.isBreakTime) {
      return 'Break Time';
    } else if (provider.isActive) {
      return 'Focus Time';
    } else {
      return 'Ready to Focus';
    }
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void _showFocusSettings(BuildContext context, FocusProvider provider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Focus Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            // Focus duration slider
            Text(
              'Focus Duration: ${provider.focusDuration} minutes',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: provider.focusDuration.toDouble(),
              min: 15,
              max: 60,
              divisions: 9,
              onChanged: (value) => provider.setFocusDuration(value.toInt()),
            ),
            const SizedBox(height: 16),
            // Break duration slider
            Text(
              'Break Duration: ${provider.breakDuration} minutes',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: provider.breakDuration.toDouble(),
              min: 5,
              max: 20,
              divisions: 3,
              onChanged: (value) => provider.setBreakDuration(value.toInt()),
            ),
            const SizedBox(height: 24),
            ADHDButton(
              text: 'Done',
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
  }

  void _showBreakDialog(BuildContext context, FocusProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Need a Break?'),
        content: const Text(
          'That\'s totally okay! ADHD brains need breaks. You can:\n\n'
          '• Take a 5-minute break now\n'
          '• Pause and resume later\n'
          '• Switch to a different task\n\n'
          'Remember: Progress is progress, no matter how small!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue'),
          ),
          ADHDButton(
            text: 'Take Break',
            onPressed: () {
              provider.startBreak();
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }
}
