import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive/hive.dart';

import 'package:focusflow/core/services/ai_service.dart';
import 'package:focusflow/core/models/task.dart';
import 'package:focusflow/core/models/user.dart';

import 'ai_service_test.mocks.dart';

@GenerateMocks([FirebaseFirestore, Box])
void main() {
  group('AIService', () {
    late MockFirebaseFirestore mockFirestore;
    late MockBox mockCache;

    setUp(() {
      mockFirestore = MockFirebaseFirestore();
      mockCache = MockBox();
    });

    group('Task Breakdown', () {
      test('should generate task breakdown with context', () async {
        // Arrange
        const taskTitle = 'Write a blog post';
        const description = 'About ADHD productivity tips';
        final user = User(
          id: 'test-user',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          subscription: const Subscription(
            tier: 'free',
            expiresAt: null,
            provider: 'none',
          ),
          preferences: const UserPreferences(
            theme: 'light',
            focusMode: 'pomodoro',
            notifications: true,
            energyTracking: true,
          ),
          stats: const UserStats(
            totalTasksCompleted: 10,
            currentStreak: 3,
            longestStreak: 7,
            totalFocusMinutes: 500,
            lastActiveDate: null,
          ),
        );

        // Act
        final breakdown = await AIService.getTaskBreakdown(
          taskTitle,
          description,
          userContext: user,
          energyLevel: 4,
        );

        // Assert
        expect(breakdown, isNotNull);
        expect(breakdown!.steps, isNotEmpty);
        expect(breakdown.steps.length, greaterThanOrEqualTo(3));
        expect(breakdown.steps.length, lessThanOrEqualTo(7));
        expect(breakdown.difficulty, greaterThan(0));
        expect(breakdown.difficulty, lessThanOrEqualTo(5));
        expect(breakdown.estimatedMinutes, greaterThan(0));
        expect(breakdown.tips, isNotEmpty);
      });

      test('should handle API errors gracefully', () async {
        // Arrange
        const taskTitle = 'Invalid task';

        // Act & Assert
        expect(
          () async => await AIService.getTaskBreakdown(taskTitle, null),
          returnsNormally,
        );
      });

      test('should use cache when available', () async {
        // Arrange
        const taskTitle = 'Cached task';
        when(mockCache.get(any)).thenReturn({
          'data': {
            'steps': ['Step 1', 'Step 2'],
            'difficulty': 3,
            'estimatedMinutes': 30,
            'tips': ['Tip 1'],
          },
          'expiry': DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch,
        });

        // Act
        final breakdown = await AIService.getTaskBreakdown(taskTitle, null);

        // Assert
        expect(breakdown, isNotNull);
        expect(breakdown!.steps, hasLength(2));
        verify(mockCache.get(any)).called(1);
      });
    });

    group('Daily Planning', () {
      test('should generate adaptive daily plan', () async {
        // Arrange
        final tasks = [
          Task(
            id: '1',
            title: 'Task 1',
            description: 'Description 1',
            priority: TaskPriority.high,
            estimatedMinutes: 30,
            userId: 'test-user',
            createdAt: DateTime.now(),
            isCompleted: false,
          ),
          Task(
            id: '2',
            title: 'Task 2',
            description: 'Description 2',
            priority: TaskPriority.medium,
            estimatedMinutes: 45,
            userId: 'test-user',
            createdAt: DateTime.now(),
            isCompleted: false,
          ),
        ];

        final user = User(
          id: 'test-user',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          subscription: const Subscription(
            tier: 'pro',
            expiresAt: null,
            provider: 'none',
          ),
          preferences: const UserPreferences(
            theme: 'light',
            focusMode: 'pomodoro',
            notifications: true,
            energyTracking: true,
          ),
          stats: const UserStats(
            totalTasksCompleted: 10,
            currentStreak: 3,
            longestStreak: 7,
            totalFocusMinutes: 500,
            lastActiveDate: null,
          ),
        );

        // Act
        final plan = await AIService.generateAdaptiveDailyPlan(
          availableTasks: tasks,
          userContext: user,
          energyLevel: 4,
          availableMinutes: 240,
        );

        // Assert
        expect(plan, isNotNull);
        expect(plan!['todaysThree'], isNotNull);
        expect(plan['schedule'], isNotNull);
        expect(plan['energyManagement'], isNotNull);
        expect(plan['adhdOptimizations'], isNotNull);
      });
    });

    group('Weekly Reflection', () {
      test('should generate weekly reflection with insights', () async {
        // Arrange
        final weekData = {
          'completedTasks': [
            {'title': 'Task 1', 'completedAt': DateTime.now().toIso8601String()},
            {'title': 'Task 2', 'completedAt': DateTime.now().toIso8601String()},
          ],
          'focusSessions': [
            {'duration': 25, 'completedAt': DateTime.now().toIso8601String()},
            {'duration': 30, 'completedAt': DateTime.now().toIso8601String()},
          ],
          'energyLevels': [3, 4, 3, 5, 4, 3, 4],
        };

        // Act
        final reflection = await AIService.generateWeeklyReflection(
          userId: 'test-user',
          weekData: weekData,
        );

        // Assert
        expect(reflection, isNotNull);
        expect(reflection!['weekSummary'], isNotNull);
        expect(reflection['keyInsights'], isNotNull);
        expect(reflection['patterns'], isNotNull);
        expect(reflection['achievements'], isNotNull);
        expect(reflection['recommendations'], isNotNull);
      });
    });

    group('Smart Nudges', () {
      test('should generate context-aware nudges', () async {
        // Arrange
        final user = User(
          id: 'test-user',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          subscription: const Subscription(
            tier: 'free',
            expiresAt: null,
            provider: 'none',
          ),
          preferences: const UserPreferences(
            theme: 'light',
            focusMode: 'pomodoro',
            notifications: true,
            energyTracking: true,
          ),
          stats: const UserStats(
            totalTasksCompleted: 10,
            currentStreak: 3,
            longestStreak: 7,
            totalFocusMinutes: 500,
            lastActiveDate: null,
          ),
        );

        final context = {
          'currentStreak': 3,
          'lastActivity': 'task_completed',
          'timeOfDay': 'morning',
          'energyLevel': 4,
        };

        // Act
        final nudge = await AIService.generateSmartNudge(
          user: user,
          context: context,
        );

        // Assert
        expect(nudge, isNotNull);
        expect(nudge!.length, lessThanOrEqualTo(200)); // Should be brief
        expect(nudge, contains(RegExp(r'[!?.]'))); // Should be encouraging
      });
    });

    group('Caching', () {
      test('should cache results with TTL', () async {
        // Arrange
        const cacheKey = 'test-cache-key';
        final data = {'test': 'data'};

        // Act
        await AIService.cacheResult(cacheKey, data, ttlHours: 1);

        // Assert
        verify(mockCache.put(cacheKey, any)).called(1);
      });

      test('should retrieve cached results', () async {
        // Arrange
        const cacheKey = 'test-cache-key';
        final cachedData = {
          'data': {'test': 'data'},
          'expiry': DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch,
        };
        when(mockCache.get(cacheKey)).thenReturn(cachedData);

        // Act
        final result = await AIService.getCachedResult(cacheKey);

        // Assert
        expect(result, isNotNull);
        expect(result!['test'], equals('data'));
      });

      test('should ignore expired cache', () async {
        // Arrange
        const cacheKey = 'test-cache-key';
        final expiredData = {
          'data': {'test': 'data'},
          'expiry': DateTime.now().subtract(const Duration(hours: 1)).millisecondsSinceEpoch,
        };
        when(mockCache.get(cacheKey)).thenReturn(expiredData);

        // Act
        final result = await AIService.getCachedResult(cacheKey);

        // Assert
        expect(result, isNull);
        verify(mockCache.delete(cacheKey)).called(1);
      });
    });

    group('Error Handling', () {
      test('should handle network errors gracefully', () async {
        // Arrange
        const taskTitle = 'Network error task';

        // Act & Assert
        expect(
          () async => await AIService.getTaskBreakdown(taskTitle, null),
          returnsNormally,
        );
      });

      test('should return fallback breakdown on parsing error', () async {
        // Arrange
        const taskTitle = 'Parsing error task';

        // Act
        final breakdown = await AIService.getTaskBreakdown(taskTitle, null);

        // Assert
        expect(breakdown, isNotNull);
        expect(breakdown!.steps, isNotEmpty);
        expect(breakdown.tips, isNotEmpty);
      });
    });

    group('Performance', () {
      test('should complete task breakdown within reasonable time', () async {
        // Arrange
        const taskTitle = 'Performance test task';
        final stopwatch = Stopwatch()..start();

        // Act
        await AIService.getTaskBreakdown(taskTitle, null);

        // Assert
        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5 seconds max
      });

      test('should handle concurrent requests', () async {
        // Arrange
        const taskTitles = ['Task 1', 'Task 2', 'Task 3'];

        // Act
        final futures = taskTitles.map(
          (title) => AIService.getTaskBreakdown(title, null),
        );
        final results = await Future.wait(futures);

        // Assert
        expect(results, hasLength(3));
        for (final result in results) {
          expect(result, isNotNull);
        }
      });
    });
  });
}
