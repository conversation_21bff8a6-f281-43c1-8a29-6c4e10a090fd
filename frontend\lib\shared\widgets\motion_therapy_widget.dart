import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// Motion therapy widget for ADHD dopamine stimulation
class MotionTherapyWidget extends StatefulWidget {
  final Widget child;
  final MotionType motionType;
  final Duration duration;
  final bool autoStart;
  final VoidCallback? onComplete;

  const MotionTherapyWidget({
    super.key,
    required this.child,
    this.motionType = MotionType.gentle,
    this.duration = const Duration(milliseconds: 1000),
    this.autoStart = true,
    this.onComplete,
  });

  @override
  State<MotionTherapyWidget> createState() => _MotionTherapyWidgetState();
}

class _MotionTherapyWidgetState extends State<MotionTherapyWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    if (widget.autoStart) {
      _controller.forward().then((_) {
        widget.onComplete?.call();
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.motionType) {
      case MotionType.gentle:
        return widget.child
            .animate(controller: _controller)
            .fadeIn(duration: 300.ms, curve: Curves.easeOut)
            .scale(begin: const Offset(0.8, 0.8), curve: Curves.elasticOut)
            .shimmer(duration: 800.ms, color: Colors.white.withOpacity(0.3));

      case MotionType.celebration:
        return widget.child
            .animate(controller: _controller)
            .scale(
              begin: const Offset(0.5, 0.5),
              end: const Offset(1.2, 1.2),
              duration: 200.ms,
              curve: Curves.elasticOut,
            )
            .then()
            .scale(
              begin: const Offset(1.2, 1.2),
              end: const Offset(1.0, 1.0),
              duration: 300.ms,
              curve: Curves.bounceOut,
            )
            .shimmer(duration: 500.ms, color: Colors.amber.withOpacity(0.5))
            .shake(duration: 200.ms, hz: 4);

      case MotionType.focus:
        return widget.child
            .animate(controller: _controller)
            .fadeIn(duration: 400.ms)
            .scale(
              begin: const Offset(0.9, 0.9),
              curve: Curves.easeInOut,
            )
            .then(delay: 200.ms)
            .shimmer(
              duration: 1000.ms,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
            );

      case MotionType.breathing:
        return widget.child
            .animate(onPlay: (controller) => controller.repeat(reverse: true))
            .scale(
              begin: const Offset(0.95, 0.95),
              end: const Offset(1.05, 1.05),
              duration: 2000.ms,
              curve: Curves.easeInOut,
            );

      case MotionType.pulse:
        return widget.child
            .animate(onPlay: (controller) => controller.repeat())
            .fadeIn(duration: 500.ms)
            .then()
            .fadeOut(duration: 500.ms);

      case MotionType.slide:
        return widget.child
            .animate(controller: _controller)
            .slideX(
              begin: -1.0,
              curve: Curves.elasticOut,
              duration: 600.ms,
            )
            .fadeIn(duration: 400.ms);

      case MotionType.bounce:
        return widget.child
            .animate(controller: _controller)
            .moveY(
              begin: -50,
              curve: Curves.bounceOut,
              duration: 800.ms,
            )
            .fadeIn(duration: 300.ms);

      case MotionType.ripple:
        return widget.child
            .animate(controller: _controller)
            .scale(
              begin: const Offset(0.8, 0.8),
              curve: Curves.elasticOut,
            )
            .then(delay: 100.ms)
            .shimmer(
              duration: 600.ms,
              color: Theme.of(context).colorScheme.secondary.withOpacity(0.3),
            );
    }
  }

  void startAnimation() {
    _controller.reset();
    _controller.forward().then((_) {
      widget.onComplete?.call();
    });
  }
}

enum MotionType {
  gentle,
  celebration,
  focus,
  breathing,
  pulse,
  slide,
  bounce,
  ripple,
}

/// Breathing exercise widget for ADHD calming
class BreathingExerciseWidget extends StatefulWidget {
  final Duration inhaleTime;
  final Duration holdTime;
  final Duration exhaleTime;
  final Color? color;
  final double size;
  final VoidCallback? onCycleComplete;

  const BreathingExerciseWidget({
    super.key,
    this.inhaleTime = const Duration(seconds: 4),
    this.holdTime = const Duration(seconds: 2),
    this.exhaleTime = const Duration(seconds: 6),
    this.color,
    this.size = 200,
    this.onCycleComplete,
  });

  @override
  State<BreathingExerciseWidget> createState() => _BreathingExerciseWidgetState();
}

class _BreathingExerciseWidgetState extends State<BreathingExerciseWidget>
    with TickerProviderStateMixin {
  late AnimationController _breathingController;
  late Animation<double> _breathingAnimation;
  String _currentPhase = 'Inhale';
  bool _isActive = false;

  @override
  void initState() {
    super.initState();
    _breathingController = AnimationController(
      duration: widget.inhaleTime + widget.holdTime + widget.exhaleTime,
      vsync: this,
    );

    _breathingAnimation = Tween<double>(
      begin: 0.7,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _breathingController,
      curve: Curves.easeInOut,
    ));

    _breathingController.addListener(_updatePhase);
  }

  @override
  void dispose() {
    _breathingController.dispose();
    super.dispose();
  }

  void _updatePhase() {
    final totalDuration = widget.inhaleTime.inMilliseconds +
        widget.holdTime.inMilliseconds +
        widget.exhaleTime.inMilliseconds;
    final currentTime = _breathingController.value * totalDuration;

    String newPhase;
    if (currentTime < widget.inhaleTime.inMilliseconds) {
      newPhase = 'Inhale';
    } else if (currentTime < widget.inhaleTime.inMilliseconds + widget.holdTime.inMilliseconds) {
      newPhase = 'Hold';
    } else {
      newPhase = 'Exhale';
    }

    if (newPhase != _currentPhase) {
      setState(() {
        _currentPhase = newPhase;
      });
    }
  }

  void startBreathing() {
    setState(() {
      _isActive = true;
    });
    _breathingController.repeat().then((_) {
      widget.onCycleComplete?.call();
    });
  }

  void stopBreathing() {
    setState(() {
      _isActive = false;
    });
    _breathingController.stop();
    _breathingController.reset();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = widget.color ?? theme.colorScheme.primary;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: _isActive ? stopBreathing : startBreathing,
          child: AnimatedBuilder(
            animation: _breathingAnimation,
            builder: (context, child) {
              return Container(
                width: widget.size * _breathingAnimation.value,
                height: widget.size * _breathingAnimation.value,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      effectiveColor.withOpacity(0.3),
                      effectiveColor.withOpacity(0.1),
                      effectiveColor.withOpacity(0.05),
                    ],
                  ),
                  border: Border.all(
                    color: effectiveColor,
                    width: 2,
                  ),
                ),
                child: Center(
                  child: Icon(
                    _isActive ? Icons.pause : Icons.play_arrow,
                    size: widget.size * 0.3,
                    color: effectiveColor,
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 24),
        Text(
          _currentPhase,
          style: theme.textTheme.headlineSmall?.copyWith(
            color: effectiveColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _isActive ? 'Tap to stop' : 'Tap to start breathing',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }
}

/// Dopamine hit animation for task completion
class DopamineHitWidget extends StatelessWidget {
  final Widget child;
  final bool trigger;
  final VoidCallback? onComplete;

  const DopamineHitWidget({
    super.key,
    required this.child,
    required this.trigger,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    if (!trigger) return child;

    return child
        .animate(onComplete: (controller) => onComplete?.call())
        .scale(
          begin: const Offset(1.0, 1.0),
          end: const Offset(1.2, 1.2),
          duration: 150.ms,
          curve: Curves.easeOut,
        )
        .then()
        .scale(
          begin: const Offset(1.2, 1.2),
          end: const Offset(1.0, 1.0),
          duration: 200.ms,
          curve: Curves.bounceOut,
        )
        .shimmer(
          duration: 300.ms,
          color: Colors.amber.withOpacity(0.5),
        );
  }
}

/// Focus ring animation for concentration
class FocusRingWidget extends StatefulWidget {
  final Widget child;
  final bool isActive;
  final Color? ringColor;
  final double ringWidth;

  const FocusRingWidget({
    super.key,
    required this.child,
    required this.isActive,
    this.ringColor,
    this.ringWidth = 3.0,
  });

  @override
  State<FocusRingWidget> createState() => _FocusRingWidgetState();
}

class _FocusRingWidgetState extends State<FocusRingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isActive) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(FocusRingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = widget.ringColor ?? theme.colorScheme.primary;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: widget.isActive
                ? Border.all(
                    color: effectiveColor.withOpacity(0.3 + 0.7 * _animation.value),
                    width: widget.ringWidth,
                  )
                : null,
          ),
          child: widget.child,
        );
      },
    );
  }
}
