import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ADHDButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ADHDButtonType type;
  final ADHDButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isEnabled;
  final Color? customColor;

  const ADHDButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ADHDButtonType.primary,
    this.size = ADHDButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isEnabled = true,
    this.customColor,
  });

  @override
  State<ADHDButton> createState() => _ADHDButtonState();
}

class _ADHDButtonState extends State<ADHDButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
      // Gentle haptic feedback for ADHD users
      HapticFeedback.lightImpact();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _handleTapEnd();
  }

  void _handleTapCancel() {
    _handleTapEnd();
  }

  void _handleTapEnd() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // Determine button colors based on type
    Color backgroundColor;
    Color foregroundColor;
    
    switch (widget.type) {
      case ADHDButtonType.primary:
        backgroundColor = widget.customColor ?? colorScheme.primary;
        foregroundColor = colorScheme.onPrimary;
        break;
      case ADHDButtonType.secondary:
        backgroundColor = widget.customColor ?? colorScheme.secondary;
        foregroundColor = colorScheme.onSecondary;
        break;
      case ADHDButtonType.success:
        backgroundColor = widget.customColor ?? const Color(0xFF28A745);
        foregroundColor = Colors.white;
        break;
      case ADHDButtonType.outline:
        backgroundColor = Colors.transparent;
        foregroundColor = widget.customColor ?? colorScheme.primary;
        break;
      case ADHDButtonType.text:
        backgroundColor = Colors.transparent;
        foregroundColor = widget.customColor ?? colorScheme.primary;
        break;
    }

    // Determine button size
    EdgeInsets padding;
    double fontSize;
    double iconSize;
    
    switch (widget.size) {
      case ADHDButtonSize.small:
        padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
        fontSize = 14;
        iconSize = 16;
        break;
      case ADHDButtonSize.medium:
        padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
        fontSize = 16;
        iconSize = 20;
        break;
      case ADHDButtonSize.large:
        padding = const EdgeInsets.symmetric(horizontal: 32, vertical: 20);
        fontSize = 18;
        iconSize = 24;
        break;
    }

    // Apply disabled state
    if (!widget.isEnabled || widget.isLoading) {
      backgroundColor = backgroundColor.withOpacity(0.5);
      foregroundColor = foregroundColor.withOpacity(0.7);
    }

    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: widget.isEnabled && !widget.isLoading ? widget.onPressed : null,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: padding,
              decoration: BoxDecoration(
                color: widget.type == ADHDButtonType.outline || widget.type == ADHDButtonType.text
                    ? Colors.transparent
                    : backgroundColor,
                border: widget.type == ADHDButtonType.outline
                    ? Border.all(color: foregroundColor, width: 2)
                    : null,
                borderRadius: BorderRadius.circular(12),
                boxShadow: widget.type != ADHDButtonType.text && widget.type != ADHDButtonType.outline
                    ? [
                        BoxShadow(
                          color: backgroundColor.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ]
                    : null,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.isLoading)
                    SizedBox(
                      width: iconSize,
                      height: iconSize,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(foregroundColor),
                      ),
                    )
                  else if (widget.icon != null)
                    Icon(
                      widget.icon,
                      size: iconSize,
                      color: foregroundColor,
                    ),
                  if ((widget.icon != null || widget.isLoading) && widget.text.isNotEmpty)
                    const SizedBox(width: 8),
                  if (widget.text.isNotEmpty)
                    Text(
                      widget.text,
                      style: TextStyle(
                        fontSize: fontSize,
                        fontWeight: FontWeight.w600,
                        color: foregroundColor,
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

enum ADHDButtonType { primary, secondary, success, outline, text }
enum ADHDButtonSize { small, medium, large }
