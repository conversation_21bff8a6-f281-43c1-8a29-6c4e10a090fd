import 'package:flutter/material.dart';

import '../../../shared/widgets/calm_card.dart';

class FeatureComparison extends StatelessWidget {
  const FeatureComparison({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return CalmCard(
      child: Column(
        children: [
          // Header
          Row(
            children: [
              const Expanded(
                flex: 2,
                child: Text('Feature'),
              ),
              Expanded(
                child: Text(
                  'Free',
                  textAlign: TextAlign.center,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  'Pro',
                  textAlign: TextAlign.center,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),
          
          // Features
          _buildFeatureRow(
            context,
            'Today\'s Three Planning',
            true,
            true,
          ),
          _buildFeatureRow(
            context,
            'Basic Focus Timer',
            true,
            true,
          ),
          _buildFeatureRow(
            context,
            'AI Task Breakdown',
            '5 per day',
            'Unlimited',
          ),
          _buildFeatureRow(
            context,
            'Advanced Focus Modes',
            false,
            true,
          ),
          _buildFeatureRow(
            context,
            'Body-Doubling Rooms',
            false,
            true,
          ),
          _buildFeatureRow(
            context,
            'Weekly AI Reflection',
            false,
            true,
          ),
          _buildFeatureRow(
            context,
            'All Themes & Customization',
            '2 themes',
            'All themes',
          ),
          _buildFeatureRow(
            context,
            'Advertisements',
            'Respectful ads',
            'Ad-free',
          ),
          _buildFeatureRow(
            context,
            'Data Export',
            false,
            true,
          ),
          _buildFeatureRow(
            context,
            'Priority Support',
            false,
            true,
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(
    BuildContext context,
    String feature,
    dynamic freeValue,
    dynamic proValue,
  ) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              feature,
              style: theme.textTheme.bodyMedium,
            ),
          ),
          Expanded(
            child: _buildFeatureValue(context, freeValue, false),
          ),
          Expanded(
            child: _buildFeatureValue(context, proValue, true),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureValue(BuildContext context, dynamic value, bool isPro) {
    final theme = Theme.of(context);
    
    if (value is bool) {
      return Icon(
        value ? Icons.check_circle : Icons.cancel,
        color: value 
            ? (isPro ? theme.colorScheme.primary : theme.colorScheme.secondary)
            : theme.colorScheme.outline,
        size: 20,
      );
    } else if (value is String) {
      return Text(
        value,
        textAlign: TextAlign.center,
        style: theme.textTheme.bodySmall?.copyWith(
          color: isPro ? theme.colorScheme.primary : null,
          fontWeight: isPro ? FontWeight.w600 : null,
        ),
      );
    }
    
    return const SizedBox.shrink();
  }
}
