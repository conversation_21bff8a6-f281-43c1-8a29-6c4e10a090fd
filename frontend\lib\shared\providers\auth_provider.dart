import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/models/user.dart' as app_user;
import '../../core/services/firebase_service.dart';

class AuthProvider extends ChangeNotifier {
  User? _firebaseUser;
  app_user.User? _appUser;
  bool _isLoading = false;
  String? _error;

  User? get firebaseUser => _firebaseUser;
  app_user.User? get appUser => _appUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _firebaseUser != null;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    FirebaseService.auth.authStateChanges().listen((User? user) {
      _firebaseUser = user;
      if (user != null) {
        _loadAppUser(user.uid);
      } else {
        _appUser = null;
      }
      notifyListeners();
    });
  }

  Future<void> _loadAppUser(String uid) async {
    try {
      final doc = await FirebaseService.firestore
          .collection('users')
          .doc(uid)
          .get();
      
      if (doc.exists) {
        _appUser = app_user.User.fromFirestore(doc);
      } else {
        // Create new user document
        await _createAppUser(uid);
      }
    } catch (e) {
      print('Load app user error: $e');
      _error = 'Failed to load user data';
    }
    notifyListeners();
  }

  Future<void> _createAppUser(String uid) async {
    try {
      final firebaseUser = FirebaseService.auth.currentUser!;
      final newUser = app_user.User(
        id: uid,
        email: firebaseUser.email ?? '',
        displayName: firebaseUser.displayName ?? 'User',
        createdAt: DateTime.now(),
        subscription: app_user.Subscription(
          tier: app_user.SubscriptionTier.free,
          provider: 'revenueCat',
        ),
        preferences: app_user.UserPreferences(
          theme: app_user.AppThemeType.light,
          focusMode: app_user.FocusModeType.pomodoro,
          notifications: true,
          energyTracking: true,
          soundEnabled: true,
          defaultFocusMinutes: 25,
        ),
        stats: app_user.UserStats(
          totalTasksCompleted: 0,
          currentStreak: 0,
          longestStreak: 0,
          totalFocusMinutes: 0,
          weeklyGoal: 3,
          lastActiveDate: DateTime.now().toIso8601String().split('T')[0],
        ),
      );

      await FirebaseService.firestore
          .collection('users')
          .doc(uid)
          .set(newUser.toFirestore());
      
      _appUser = newUser;
      
      // Log user creation event
      await FirebaseService.logEvent('user_created', {
        'user_id': uid,
        'signup_method': 'email',
      });
    } catch (e) {
      print('Create app user error: $e');
      _error = 'Failed to create user profile';
    }
  }

  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _setLoading(true);
      _error = null;
      
      await FirebaseService.auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      // Log sign in event
      await FirebaseService.logEvent('login', {
        'method': 'email',
      });
      
      return true;
    } on FirebaseAuthException catch (e) {
      _error = _getAuthErrorMessage(e.code);
      return false;
    } catch (e) {
      _error = 'An unexpected error occurred';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> createUserWithEmailAndPassword(String email, String password, String displayName) async {
    try {
      _setLoading(true);
      _error = null;
      
      final credential = await FirebaseService.auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      // Update display name
      await credential.user?.updateDisplayName(displayName);
      
      // Log sign up event
      await FirebaseService.logEvent('sign_up', {
        'method': 'email',
      });
      
      return true;
    } on FirebaseAuthException catch (e) {
      _error = _getAuthErrorMessage(e.code);
      return false;
    } catch (e) {
      _error = 'An unexpected error occurred';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    try {
      await FirebaseService.auth.signOut();
      _appUser = null;
      
      // Log sign out event
      await FirebaseService.logEvent('logout', {});
    } catch (e) {
      print('Sign out error: $e');
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _error = null;
      
      await FirebaseService.auth.sendPasswordResetEmail(email: email);
      return true;
    } on FirebaseAuthException catch (e) {
      _error = _getAuthErrorMessage(e.code);
      return false;
    } catch (e) {
      _error = 'An unexpected error occurred';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateUserPreferences(app_user.UserPreferences preferences) async {
    if (_appUser == null) return;
    
    try {
      final updatedUser = _appUser!.copyWith(preferences: preferences);
      
      await FirebaseService.firestore
          .collection('users')
          .doc(_appUser!.id)
          .update({'preferences': preferences.toMap()});
      
      _appUser = updatedUser;
      notifyListeners();
    } catch (e) {
      print('Update preferences error: $e');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  String _getAuthErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      default:
        return 'Authentication failed. Please try again.';
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
