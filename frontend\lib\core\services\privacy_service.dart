import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:crypto/crypto.dart';

import '../models/user.dart' as app_user;
import '../models/task.dart';
import 'analytics_service.dart';

class PrivacyService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Export all user data in JSON format (GDPR compliance)
  static Future<String?> exportUserData() async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      final exportData = <String, dynamic>{};
      
      // User profile data
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        exportData['profile'] = userDoc.data();
      }

      // Tasks data
      final tasksSnapshot = await _firestore
          .collection('tasks')
          .where('userId', isEqualTo: user.uid)
          .get();
      exportData['tasks'] = tasksSnapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();

      // Focus sessions data
      final focusSnapshot = await _firestore
          .collection('focusSessions')
          .where('userId', isEqualTo: user.uid)
          .get();
      exportData['focusSessions'] = focusSnapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();

      // Daily plans data
      final plansSnapshot = await _firestore
          .collection('dailyPlans')
          .where('userId', isEqualTo: user.uid)
          .get();
      exportData['dailyPlans'] = plansSnapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();

      // Weekly reflections data
      final reflectionsSnapshot = await _firestore
          .collection('weeklyReflections')
          .where('userId', isEqualTo: user.uid)
          .get();
      exportData['weeklyReflections'] = reflectionsSnapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();

      // Add metadata
      exportData['exportMetadata'] = {
        'exportDate': DateTime.now().toIso8601String(),
        'userId': user.uid,
        'email': user.email,
        'appVersion': '1.0.0',
        'dataFormat': 'JSON',
      };

      // Convert to JSON string
      final jsonString = const JsonEncoder.withIndent('  ').convert(exportData);
      
      // Save to file
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/focusflow_data_export_${DateTime.now().millisecondsSinceEpoch}.json');
      await file.writeAsString(jsonString);

      await AnalyticsService.logEvent('data_exported', parameters: {
        'export_size_kb': (jsonString.length / 1024).round(),
        'collections_count': exportData.keys.length - 1, // Exclude metadata
      });

      return file.path;
    } catch (e) {
      debugPrint('Data export error: $e');
      await AnalyticsService.logError(
        error: e.toString(),
        context: 'data_export',
      );
      return null;
    }
  }

  /// Share exported data via system share sheet
  static Future<bool> shareExportedData(String filePath) async {
    try {
      final result = await Share.shareXFiles(
        [XFile(filePath)],
        text: 'My FocusFlow data export',
        subject: 'FocusFlow Data Export',
      );

      await AnalyticsService.logEvent('data_shared', parameters: {
        'share_result': result.status.name,
      });

      return result.status == ShareResultStatus.success;
    } catch (e) {
      debugPrint('Data sharing error: $e');
      return false;
    }
  }

  /// Delete all user data (GDPR right to be forgotten)
  static Future<bool> deleteAllUserData() async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      final batch = _firestore.batch();

      // Delete user profile
      batch.delete(_firestore.collection('users').doc(user.uid));

      // Delete tasks
      final tasksSnapshot = await _firestore
          .collection('tasks')
          .where('userId', isEqualTo: user.uid)
          .get();
      for (final doc in tasksSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete focus sessions
      final focusSnapshot = await _firestore
          .collection('focusSessions')
          .where('userId', isEqualTo: user.uid)
          .get();
      for (final doc in focusSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete daily plans
      final plansSnapshot = await _firestore
          .collection('dailyPlans')
          .where('userId', isEqualTo: user.uid)
          .get();
      for (final doc in plansSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete weekly reflections
      final reflectionsSnapshot = await _firestore
          .collection('weeklyReflections')
          .where('userId', isEqualTo: user.uid)
          .get();
      for (final doc in reflectionsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete AI cache entries
      final cacheSnapshot = await _firestore
          .collection('ai_cache')
          .where('userId', isEqualTo: user.uid)
          .get();
      for (final doc in cacheSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Commit batch delete
      await batch.commit();

      // Delete user files from Storage
      try {
        final storageRef = _storage.ref().child('users/${user.uid}');
        final listResult = await storageRef.listAll();
        for (final item in listResult.items) {
          await item.delete();
        }
      } catch (e) {
        debugPrint('Storage deletion error: $e');
      }

      // Delete Firebase Auth account
      await user.delete();

      await AnalyticsService.logEvent('account_deleted', parameters: {
        'deletion_method': 'user_request',
      });

      return true;
    } catch (e) {
      debugPrint('Account deletion error: $e');
      await AnalyticsService.logError(
        error: e.toString(),
        context: 'account_deletion',
      );
      return false;
    }
  }

  /// Anonymize user data (alternative to deletion)
  static Future<bool> anonymizeUserData() async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      final anonymousId = _generateAnonymousId();
      final batch = _firestore.batch();

      // Anonymize user profile
      final userRef = _firestore.collection('users').doc(user.uid);
      batch.update(userRef, {
        'email': '<EMAIL>',
        'displayName': 'Anonymous User',
        'anonymizedAt': FieldValue.serverTimestamp(),
        'anonymousId': anonymousId,
      });

      // Anonymize tasks (keep data for research but remove PII)
      final tasksSnapshot = await _firestore
          .collection('tasks')
          .where('userId', isEqualTo: user.uid)
          .get();
      for (final doc in tasksSnapshot.docs) {
        batch.update(doc.reference, {
          'title': _anonymizeText(doc.data()['title'] ?? ''),
          'description': _anonymizeText(doc.data()['description'] ?? ''),
          'anonymizedAt': FieldValue.serverTimestamp(),
        });
      }

      await batch.commit();

      await AnalyticsService.logEvent('data_anonymized', parameters: {
        'anonymous_id': anonymousId,
      });

      return true;
    } catch (e) {
      debugPrint('Data anonymization error: $e');
      return false;
    }
  }

  /// Check and request necessary permissions
  static Future<Map<String, bool>> checkPrivacyPermissions() async {
    final permissions = <String, bool>{};

    // Storage permission for data export
    final storageStatus = await Permission.storage.status;
    permissions['storage'] = storageStatus.isGranted;

    // Notification permission
    final notificationStatus = await Permission.notification.status;
    permissions['notifications'] = notificationStatus.isGranted;

    // Camera permission (for potential future features)
    final cameraStatus = await Permission.camera.status;
    permissions['camera'] = cameraStatus.isGranted;

    return permissions;
  }

  /// Request specific permission
  static Future<bool> requestPermission(Permission permission) async {
    final status = await permission.request();
    return status.isGranted;
  }

  /// Get privacy settings summary
  static Future<Map<String, dynamic>> getPrivacySettings() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return {};

      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) return {};

      final userData = userDoc.data()!;
      final preferences = userData['preferences'] as Map<String, dynamic>? ?? {};

      return {
        'dataCollection': preferences['dataCollection'] ?? true,
        'analytics': preferences['analytics'] ?? true,
        'notifications': preferences['notifications'] ?? true,
        'crashReporting': preferences['crashReporting'] ?? true,
        'personalizedAds': preferences['personalizedAds'] ?? false,
        'dataSharing': preferences['dataSharing'] ?? false,
        'accountCreated': userData['createdAt'],
        'lastUpdated': userData['updatedAt'],
      };
    } catch (e) {
      debugPrint('Privacy settings error: $e');
      return {};
    }
  }

  /// Update privacy settings
  static Future<bool> updatePrivacySettings(Map<String, dynamic> settings) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _firestore.collection('users').doc(user.uid).update({
        'preferences.dataCollection': settings['dataCollection'],
        'preferences.analytics': settings['analytics'],
        'preferences.notifications': settings['notifications'],
        'preferences.crashReporting': settings['crashReporting'],
        'preferences.personalizedAds': settings['personalizedAds'],
        'preferences.dataSharing': settings['dataSharing'],
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await AnalyticsService.logEvent('privacy_settings_updated', parameters: {
        'data_collection': settings['dataCollection'],
        'analytics': settings['analytics'],
        'notifications': settings['notifications'],
      });

      return true;
    } catch (e) {
      debugPrint('Privacy settings update error: $e');
      return false;
    }
  }

  /// Generate anonymous ID for research purposes
  static String _generateAnonymousId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = timestamp.toString() + DateTime.now().microsecond.toString();
    final bytes = utf8.encode(random);
    final digest = sha256.convert(bytes);
    return 'anon_${digest.toString().substring(0, 16)}';
  }

  /// Anonymize text by replacing with generic terms
  static String _anonymizeText(String text) {
    if (text.isEmpty) return text;
    
    // Replace with generic task descriptions
    final words = text.split(' ');
    if (words.length <= 2) return 'Task';
    if (words.length <= 5) return 'Short task';
    return 'Long task';
  }

  /// Clear local app data
  static Future<void> clearLocalData() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync();
      
      for (final file in files) {
        if (file is File && file.path.contains('focusflow')) {
          await file.delete();
        }
      }

      await AnalyticsService.logEvent('local_data_cleared');
    } catch (e) {
      debugPrint('Local data clearing error: $e');
    }
  }
}
