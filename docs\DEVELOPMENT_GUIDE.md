# FocusFlow Development Guide

## Getting Started

### Development Environment Setup

#### Prerequisites
- **Flutter**: 3.16.0 or higher
- **Node.js**: 18.0 or higher
- **Firebase CLI**: Latest version
- **Git**: Latest version
- **IDE**: VS Code with Flutter/Dart extensions (recommended)

#### Initial Setup
\`\`\`bash
# Clone the repository
git clone https://github.com/focusflow/focusflow-app.git
cd focusflow-app

# Setup Firebase
firebase login
firebase use --add  # Select your Firebase project

# Backend setup
cd backend/functions
npm install
npm run build

# Frontend setup
cd ../../frontend
flutter pub get
flutterfire configure
\`\`\`

### Development Workflow

#### Branch Strategy
- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: Individual feature development
- `hotfix/*`: Critical production fixes

#### Daily Development
\`\`\`bash
# Start your day
git checkout develop
git pull origin develop
git checkout -b feature/your-feature-name

# Start Firebase emulators
cd backend
firebase emulators:start

# In another terminal, run Flutter
cd frontend
flutter run --debug
\`\`\`

## Code Architecture

### Frontend Architecture (Flutter)

#### Folder Structure
\`\`\`
lib/
├── app/                    # App-level configuration
│   ├── app.dart           # Main app widget
│   ├── theme.dart         # App theme and colors
│   └── routes.dart        # Navigation routes
├── core/                  # Core business logic
│   ├── models/            # Data models
│   ├── services/          # API and external services
│   ├── utils/             # Utility functions
│   └── constants/         # App constants
├── features/              # Feature modules
│   ├── auth/              # Authentication
│   ├── today/             # Today's Three feature
│   ├── focus/             # Focus mode
│   └── analytics/         # User analytics
└── shared/                # Shared components
    ├── widgets/           # Reusable widgets
    ├── providers/         # State management
    └── extensions/        # Dart extensions
\`\`\`

#### State Management Pattern
We use Provider pattern with ChangeNotifier for state management:

\`\`\`dart
// Provider setup
class TaskProvider extends ChangeNotifier {
  List<Task> _tasks = [];
  bool _isLoading = false;

  List<Task> get tasks => _tasks;
  bool get isLoading => _isLoading;

  Future<void> loadTasks() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      _tasks = await _taskService.getTasks();
    } catch (e) {
      // Handle error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}

// Widget usage
class TaskListWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<TaskProvider>(
      builder: (context, taskProvider, child) {
        if (taskProvider.isLoading) {
          return CircularProgressIndicator();
        }
        
        return ListView.builder(
          itemCount: taskProvider.tasks.length,
          itemBuilder: (context, index) {
            return TaskTile(task: taskProvider.tasks[index]);
          },
        );
      },
    );
  }
}
\`\`\`

#### ADHD-Friendly UI Guidelines

##### Color System
\`\`\`dart
// Use our ADHD-optimized color palette
class ADHDColors {
  static const primary = Color(0xFF6366F1);      // Calming indigo
  static const success = Color(0xFF10B981);      // Dopamine green
  static const warning = Color(0xFFF59E0B);      // Gentle amber
  static const error = Color(0xFFEF4444);        // Soft red
  static const background = Color(0xFFF8FAFC);   // Calm background
  static const surface = Color(0xFFFFFFFF);      // Clean white
}
\`\`\`

##### Typography
\`\`\`dart
// ADHD-friendly text styles
class ADHDTextStyles {
  static const heading1 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    height: 1.3,  // Improved readability
    letterSpacing: -0.5,
  );
  
  static const body = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 1.5,  // Generous line height
    letterSpacing: 0.1,
  );
}
\`\`\`

##### Component Guidelines
\`\`\`dart
// ADHD Button - larger touch targets, clear feedback
class ADHDButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56,  // Minimum 44pt touch target
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: ADHDColors.primary,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: isLoading 
          ? CircularProgressIndicator(color: Colors.white)
          : Text(text, style: ADHDTextStyles.buttonText),
      ),
    );
  }
}
\`\`\`

### Backend Architecture (Firebase Functions)

#### Project Structure
\`\`\`
functions/src/
├── index.ts               # Main entry point
├── models/                # TypeScript interfaces
│   ├── user.ts
│   ├── task.ts
│   └── analytics.ts
├── services/              # Business logic
│   ├── aiService.ts       # OpenAI integration
│   ├── taskService.ts     # Task management
│   └── analyticsService.ts
├── middleware/            # Express middleware
│   ├── auth.ts           # Authentication
│   ├── validation.ts     # Input validation
│   └── rateLimit.ts      # Rate limiting
└── utils/                # Helper functions
    ├── cache.ts          # Redis caching
    ├── logger.ts         # Logging utilities
    └── errors.ts         # Error handling
\`\`\`

#### API Design Patterns

##### RESTful Endpoints
\`\`\`typescript
// Task service example
export class TaskService {
  async getTasks(userId: string, date: string): Promise<Task[]> {
    const tasksRef = db.collection('tasks')
      .where('userId', '==', userId)
      .where('date', '==', date)
      .orderBy('createdAt', 'desc');
    
    const snapshot = await tasksRef.get();
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Task));
  }

  async createTask(userId: string, taskData: CreateTaskRequest): Promise<Task> {
    const task: Task = {
      id: '',
      userId,
      ...taskData,
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const docRef = await db.collection('tasks').add(task);
    return { ...task, id: docRef.id };
  }
}
\`\`\`

##### Error Handling
\`\`\`typescript
// Centralized error handling
export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message);
  }
}

// Error middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (error instanceof AppError) {
    return res.status(error.statusCode).json({
      error: error.message,
      code: error.code,
      timestamp: new Date().toISOString(),
    });
  }

  // Log unexpected errors
  console.error('Unexpected error:', error);
  
  res.status(500).json({
    error: 'Internal server error',
    code: 'INTERNAL_ERROR',
    timestamp: new Date().toISOString(),
  });
};
\`\`\`

## Testing Strategy

### Frontend Testing

#### Unit Tests
\`\`\`dart
// Test task model
void main() {
  group('Task Model', () {
    test('should create task from JSON', () {
      final json = {
        'id': 'task_123',
        'title': 'Test task',
        'status': 'pending',
        'createdAt': '2024-01-15T10:00:00Z',
      };

      final task = Task.fromJson(json);

      expect(task.id, 'task_123');
      expect(task.title, 'Test task');
      expect(task.status, TaskStatus.pending);
    });
  });
}
\`\`\`

#### Widget Tests
\`\`\`dart
// Test ADHD button widget
void main() {
  testWidgets('ADHDButton should show loading state', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: ADHDButton(
          text: 'Test Button',
          isLoading: true,
          onPressed: () {},
        ),
      ),
    );

    expect(find.byType(CircularProgressIndicator), findsOneWidget);
    expect(find.text('Test Button'), findsNothing);
  });
}
\`\`\`

#### Integration Tests
\`\`\`dart
// Test complete user flow
void main() {
  group('Task Creation Flow', () {
    testWidgets('should create and display new task', (tester) async {
      // Setup mock services
      final mockTaskService = MockTaskService();
      
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(
              create: (_) => TaskProvider(mockTaskService),
            ),
          ],
          child: MyApp(),
        ),
      );

      // Navigate to task creation
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Fill form and submit
      await tester.enterText(find.byKey(Key('task_title')), 'New Task');
      await tester.tap(find.byKey(Key('create_button')));
      await tester.pumpAndSettle();

      // Verify task appears in list
      expect(find.text('New Task'), findsOneWidget);
    });
  });
}
\`\`\`

### Backend Testing

#### Unit Tests
\`\`\`typescript
// Test AI service
describe('AIService', () => {
  let aiService: AIService;
  let mockOpenAI: jest.Mocked<OpenAI>;

  beforeEach(() => {
    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    } as any;
    
    aiService = new AIService(mockOpenAI);
  });

  it('should break down task into subtasks', async () => {
    const mockResponse = {
      choices: [{
        message: {
          content: JSON.stringify({
            subtasks: [
              { title: 'Subtask 1', estimatedMinutes: 15 },
              { title: 'Subtask 2', estimatedMinutes: 20 },
            ],
          }),
        },
      }],
    };

    mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

    const result = await aiService.breakdownTask({
      taskTitle: 'Complex Task',
      taskDescription: 'A complex task to break down',
      userContext: { energyLevel: 'medium' },
    });

    expect(result.subtasks).toHaveLength(2);
    expect(result.subtasks[0].title).toBe('Subtask 1');
  });
});
\`\`\`

#### Integration Tests
\`\`\`typescript
// Test complete API endpoints
describe('Task API', () => {
  let app: Express;
  let testUser: { uid: string; token: string };

  beforeAll(async () => {
    app = createTestApp();
    testUser = await createTestUser();
  });

  it('should create and retrieve task', async () => {
    const taskData = {
      title: 'Test Task',
      description: 'A test task',
      priority: 'medium',
    };

    // Create task
    const createResponse = await request(app)
      .post('/api/tasks')
      .set('Authorization', `Bearer ${testUser.token}`)
      .send(taskData)
      .expect(201);

    const taskId = createResponse.body.task.id;

    // Retrieve task
    const getResponse = await request(app)
      .get(`/api/tasks/${taskId}`)
      .set('Authorization', `Bearer ${testUser.token}`)
      .expect(200);

    expect(getResponse.body.task.title).toBe(taskData.title);
  });
});
\`\`\`

## Performance Optimization

### Frontend Performance

#### Image Optimization
\`\`\`dart
// Optimized image loading
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      placeholder: (context, url) => Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          width: width,
          height: height,
          color: Colors.white,
        ),
      ),
      errorWidget: (context, url, error) => Icon(Icons.error),
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
    );
  }
}
\`\`\`

#### List Performance
\`\`\`dart
// Efficient list rendering
class TaskList extends StatelessWidget {
  final List<Task> tasks;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: tasks.length,
      itemExtent: 80.0,  // Fixed height for better performance
      itemBuilder: (context, index) {
        return TaskTile(
          key: ValueKey(tasks[index].id),  // Stable keys
          task: tasks[index],
        );
      },
    );
  }
}
\`\`\`

### Backend Performance

#### Caching Strategy
\`\`\`typescript
// Redis caching for AI responses
export class CacheService {
  private redis: Redis;

  async getCachedAIResponse(prompt: string): Promise<string | null> {
    const key = `ai:${this.hashPrompt(prompt)}`;
    return await this.redis.get(key);
  }

  async setCachedAIResponse(
    prompt: string, 
    response: string, 
    ttl: number = 3600
  ): Promise<void> {
    const key = `ai:${this.hashPrompt(prompt)}`;
    await this.redis.setex(key, ttl, response);
  }

  private hashPrompt(prompt: string): string {
    return crypto.createHash('sha256').update(prompt).digest('hex');
  }
}
\`\`\`

#### Database Optimization
\`\`\`typescript
// Efficient Firestore queries
export class TaskService {
  async getTasksOptimized(
    userId: string, 
    date: string, 
    limit: number = 50
  ): Promise<Task[]> {
    // Use composite index: userId + date + createdAt
    const query = db.collection('tasks')
      .where('userId', '==', userId)
      .where('date', '==', date)
      .orderBy('createdAt', 'desc')
      .limit(limit);

    const snapshot = await query.get();
    
    // Use converter for type safety
    return snapshot.docs.map(doc => 
      doc.data() as Task
    );
  }
}
\`\`\`

## Debugging

### Frontend Debugging

#### Flutter Inspector
\`\`\`bash
# Enable Flutter Inspector
flutter run --debug
# Then open DevTools in browser
\`\`\`

#### Logging
\`\`\`dart
// Structured logging
class Logger {
  static void info(String message, [Map<String, dynamic>? data]) {
    print('[INFO] $message ${data != null ? jsonEncode(data) : ''}');
  }

  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    print('[ERROR] $message');
    if (error != null) print('Error: $error');
    if (stackTrace != null) print('Stack: $stackTrace');
  }
}

// Usage
Logger.info('Task created', {'taskId': task.id, 'userId': user.id});
Logger.error('Failed to create task', error, stackTrace);
\`\`\`

### Backend Debugging

#### Firebase Functions Logs
\`\`\`bash
# View logs in real-time
firebase functions:log --follow

# View specific function logs
firebase functions:log --only taskService
\`\`\`

#### Local Debugging
\`\`\`typescript
// Debug middleware
export const debugMiddleware = (
  req: Request, 
  res: Response, 
  next: NextFunction
) => {
  console.log(`[DEBUG] ${req.method} ${req.path}`, {
    headers: req.headers,
    body: req.body,
    query: req.query,
  });
  
  next();
};
\`\`\`

## Deployment

### Staging Deployment
\`\`\`bash
# Deploy to staging
firebase use staging
firebase deploy --only functions,firestore

# Deploy frontend to staging
cd frontend
flutter build apk --flavor staging
\`\`\`

### Production Deployment
\`\`\`bash
# Deploy to production
firebase use production
firebase deploy --only functions,firestore,hosting

# Deploy to app stores
cd frontend
flutter build appbundle --release  # Android
flutter build ipa --release        # iOS
\`\`\`

## Troubleshooting

### Common Issues

#### Flutter Build Issues
\`\`\`bash
# Clean and rebuild
flutter clean
flutter pub get
flutter pub deps
flutter build apk
\`\`\`

#### Firebase Connection Issues
\`\`\`bash
# Reconfigure Firebase
flutterfire configure
firebase login --reauth
\`\`\`

#### Performance Issues
- Use Flutter DevTools Profiler
- Check for memory leaks with `flutter analyze`
- Monitor network requests in DevTools

#### Backend Issues
- Check Firebase Functions logs
- Verify environment variables
- Test with Firebase Emulators

---

This development guide should help you contribute effectively to FocusFlow. For additional help, check our [Contributing Guidelines](CONTRIBUTING.md) or reach out to the team on Discord.
