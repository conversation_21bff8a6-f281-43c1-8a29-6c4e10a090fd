import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:uuid/uuid.dart';

import '../../../core/models/task.dart';
import '../../../shared/widgets/adhd_button.dart';

class QuickAddTask extends StatefulWidget {
  final Function(Task) onTaskAdded;

  const QuickAddTask({
    super.key,
    required this.onTaskAdded,
  });

  @override
  State<QuickAddTask> createState() => _QuickAddTaskState();
}

class _QuickAddTaskState extends State<QuickAddTask> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  TaskPriority _selectedPriority = TaskPriority.medium;
  int _estimatedMinutes = 30;
  bool _isLoading = false;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 16,
        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.outline.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 20),
          
          // Title
          Text(
            'Add Quick Task',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 20),
          
          // Task title input
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'What needs to be done?',
              hintText: 'e.g., Review project proposal',
            ),
            textCapitalization: TextCapitalization.sentences,
            onChanged: (_) => setState(() {}),
          ),
          const SizedBox(height: 16),
          
          // Description input
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description (optional)',
              hintText: 'Add any helpful details...',
            ),
            textCapitalization: TextCapitalization.sentences,
            maxLines: 2,
          ),
          const SizedBox(height: 20),
          
          // Priority selection
          Text(
            'Priority',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: TaskPriority.values.map((priority) {
              final isSelected = _selectedPriority == priority;
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      setState(() => _selectedPriority = priority);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? _getPriorityColor(priority).withOpacity(0.2)
                            : theme.colorScheme.surfaceVariant.withOpacity(0.3),
                        border: Border.all(
                          color: isSelected 
                              ? _getPriorityColor(priority)
                              : theme.colorScheme.outline.withOpacity(0.3),
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getPriorityLabel(priority),
                        textAlign: TextAlign.center,
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: isSelected 
                              ? _getPriorityColor(priority)
                              : theme.colorScheme.onSurface.withOpacity(0.7),
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 20),
          
          // Time estimate
          Text(
            'Estimated Time: ${_getTimeLabel(_estimatedMinutes)}',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Slider(
            value: _estimatedMinutes.toDouble(),
            min: 5,
            max: 120,
            divisions: 23,
            label: _getTimeLabel(_estimatedMinutes),
            onChanged: (value) {
              HapticFeedback.selectionClick();
              setState(() => _estimatedMinutes = value.round());
            },
          ),
          const SizedBox(height: 24),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: ADHDButton(
                  text: 'Cancel',
                  type: ADHDButtonType.outline,
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ADHDButton(
                  text: 'Add Task',
                  type: ADHDButtonType.primary,
                  isLoading: _isLoading,
                  isEnabled: _titleController.text.trim().isNotEmpty,
                  onPressed: _addTask,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _addTask() async {
    if (_titleController.text.trim().isEmpty) return;
    
    setState(() => _isLoading = true);
    
    try {
      final task = Task(
        id: const Uuid().v4(),
        userId: 'current_user_id', // This would come from auth provider
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        priority: _selectedPriority,
        estimatedMinutes: _estimatedMinutes,
        status: TaskStatus.todo,
        createdAt: DateTime.now(),
        dueDate: DateTime.now(),
      );
      
      // Add haptic feedback for success
      HapticFeedback.mediumImpact();
      
      widget.onTaskAdded(task);
    } catch (e) {
      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add task: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return const Color(0xFFDC3545);
      case TaskPriority.medium:
        return const Color(0xFFF5A623);
      case TaskPriority.low:
        return const Color(0xFF28A745);
    }
  }

  String _getPriorityLabel(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return 'High';
      case TaskPriority.medium:
        return 'Medium';
      case TaskPriority.low:
        return 'Low';
    }
  }

  String _getTimeLabel(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? '${hours}h ${remainingMinutes}m' : '${hours}h';
    }
  }
}
