import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../core/models/task.dart';
import '../../../core/models/user.dart';
import '../../../shared/widgets/calm_card.dart';
import '../../../shared/widgets/adhd_button.dart';
import '../widgets/todays_three_widget.dart';
import '../widgets/energy_tracker.dart';
import '../widgets/quick_add_task.dart';
import '../widgets/dopamine_celebration.dart';
import '../providers/today_provider.dart';

class TodayScreen extends StatefulWidget {
  const TodayScreen({super.key});

  @override
  State<TodayScreen> createState() => _TodayScreenState();
}

class _TodayScreenState extends State<TodayScreen> with TickerProviderStateMixin {
  late AnimationController _celebrationController;
  late AnimationController _fadeController;

  @override
  void initState() {
    super.initState();
    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    // Load today's data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TodayProvider>().loadTodaysData();
      _fadeController.forward();
    });
  }

  @override
  void dispose() {
    _celebrationController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Scaffold(
      backgroundColor: colorScheme.background,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeController,
          child: Consumer<TodayProvider>(
            builder: (context, todayProvider, child) {
              return CustomScrollView(
                slivers: [
                  _buildAppBar(context, todayProvider),
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        const SizedBox(height: 16),
                        _buildGreeting(context, todayProvider),
                        const SizedBox(height: 24),
                        _buildEnergyTracker(context, todayProvider),
                        const SizedBox(height: 24),
                        _buildTodaysThree(context, todayProvider),
                        const SizedBox(height: 24),
                        _buildQuickActions(context, todayProvider),
                        const SizedBox(height: 24),
                        _buildProgressSummary(context, todayProvider),
                        const SizedBox(height: 100), // Bottom padding for nav
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  Widget _buildAppBar(BuildContext context, TodayProvider provider) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.background,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Today',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
        titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.insights_outlined),
          onPressed: () => context.push('/reflection'),
          tooltip: 'Weekly Reflection',
        ),
        IconButton(
          icon: const Icon(Icons.settings_outlined),
          onPressed: () => context.push('/profile/settings'),
          tooltip: 'Settings',
        ),
      ],
    );
  }

  Widget _buildGreeting(BuildContext context, TodayProvider provider) {
    final theme = Theme.of(context);
    final now = DateTime.now();
    final hour = now.hour;
    
    String greeting;
    String encouragement;
    
    if (hour < 12) {
      greeting = 'Good Morning!';
      encouragement = 'Ready to make today amazing?';
    } else if (hour < 17) {
      greeting = 'Good Afternoon!';
      encouragement = 'You\'re doing great so far!';
    } else {
      greeting = 'Good Evening!';
      encouragement = 'Let\'s finish strong!';
    }

    return CalmCard(
      backgroundColor: theme.colorScheme.primaryContainer.withOpacity(0.3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            greeting,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            encouragement,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
          if (provider.currentStreak > 0) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: theme.colorScheme.secondary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.local_fire_department,
                    size: 16,
                    color: theme.colorScheme.secondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${provider.currentStreak} day streak!',
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: theme.colorScheme.secondary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEnergyTracker(BuildContext context, TodayProvider provider) {
    return CalmCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'How\'s your energy today?',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          EnergyTracker(
            currentLevel: provider.energyLevel,
            onLevelChanged: (level) {
              provider.updateEnergyLevel(level);
              // Trigger adaptive planning
              provider.generateAdaptivePlan();
            },
          ),
          if (provider.energyLevel > 0) ...[
            const SizedBox(height: 12),
            Text(
              _getEnergyAdvice(provider.energyLevel),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTodaysThree(BuildContext context, TodayProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'Today\'s Three',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (provider.todaysThree.length < 3)
                TextButton.icon(
                  onPressed: () => _showAddTaskDialog(context, provider),
                  icon: const Icon(Icons.add, size: 18),
                  label: const Text('Add Task'),
                ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        TodaysThreeWidget(
          tasks: provider.todaysThree,
          onTaskTap: (task) => _handleTaskTap(context, task),
          onTaskComplete: (task) => _handleTaskComplete(context, provider, task),
          onTaskReorder: (oldIndex, newIndex) => 
              provider.reorderTodaysThree(oldIndex, newIndex),
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context, TodayProvider provider) {
    final theme = Theme.of(context);
    
    return CalmCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ADHDButton(
                  text: 'Start Focus',
                  icon: Icons.psychology,
                  type: ADHDButtonType.primary,
                  onPressed: () => context.push('/focus'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ADHDButton(
                  text: 'Body Double',
                  icon: Icons.people,
                  type: ADHDButtonType.secondary,
                  onPressed: () => context.push('/focus/body-doubling'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ADHDButton(
            text: 'Add New Task',
            icon: Icons.add_task,
            type: ADHDButtonType.outline,
            onPressed: () => context.push('/tasks/add'),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSummary(BuildContext context, TodayProvider provider) {
    final theme = Theme.of(context);
    final completedToday = provider.completedTasksToday;
    final totalTasks = provider.todaysThree.length;
    final progress = totalTasks > 0 ? completedToday / totalTasks : 0.0;
    
    return CalmCard(
      backgroundColor: theme.colorScheme.secondaryContainer.withOpacity(0.3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Today\'s Progress',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '$completedToday/$totalTasks',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.secondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.secondary),
            minHeight: 8,
          ),
          const SizedBox(height: 12),
          Text(
            _getProgressMessage(progress),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () => context.push('/tasks/add'),
      icon: const Icon(Icons.add),
      label: const Text('Add Task'),
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
    );
  }

  void _handleTaskTap(BuildContext context, Task task) {
    if (task.aiBreakdown != null) {
      context.push('/today/breakdown/${task.id}');
    } else {
      // Generate AI breakdown first
      context.read<TodayProvider>().generateTaskBreakdown(task.id);
    }
  }

  void _handleTaskComplete(BuildContext context, TodayProvider provider, Task task) {
    provider.completeTask(task.id);
    
    // Show celebration animation
    _celebrationController.forward().then((_) {
      _celebrationController.reset();
    });
    
    // Show dopamine celebration
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => DopamineCelebration(
        message: _getCelebrationMessage(),
        onDismiss: () => Navigator.of(context).pop(),
      ),
    );
  }

  void _showAddTaskDialog(BuildContext context, TodayProvider provider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => QuickAddTask(
        onTaskAdded: (task) {
          provider.addToTodaysThree(task);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  String _getEnergyAdvice(int level) {
    switch (level) {
      case 1:
        return 'Low energy day? That\'s okay! Focus on small, easy wins.';
      case 2:
        return 'Gentle start today. Pick tasks that feel manageable.';
      case 3:
        return 'Balanced energy! Good day for steady progress.';
      case 4:
        return 'High energy! Great time for challenging tasks.';
      case 5:
        return 'Peak energy! You can tackle anything today!';
      default:
        return '';
    }
  }

  String _getProgressMessage(double progress) {
    if (progress == 0) {
      return 'Ready to start? Every journey begins with a single step!';
    } else if (progress < 0.5) {
      return 'Great start! You\'re building momentum.';
    } else if (progress < 1.0) {
      return 'Awesome progress! You\'re more than halfway there!';
    } else {
      return 'Amazing! You\'ve completed all your tasks for today! 🎉';
    }
  }

  String _getCelebrationMessage() {
    final messages = [
      'Fantastic work! 🌟',
      'You did it! 🎉',
      'Another win! 💪',
      'Keep it up! ⭐',
      'Crushing it! 🚀',
      'Well done! 👏',
    ];
    return messages[DateTime.now().millisecond % messages.length];
  }
}
