import type { Request, Response, NextFunction } from "express"
import rateLimit from "express-rate-limit"
import helmet from "helmet"

// Rate limiting configuration
export const createRateLimit = (windowMs: number, max: number) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      error: "Too many requests from this IP, please try again later.",
      retryAfter: Math.ceil(windowMs / 1000),
    },
    standardHeaders: true,
    legacyHeaders: false,
  })
}

// General API rate limit: 100 requests per 15 minutes
export const generalRateLimit = createRateLimit(15 * 60 * 1000, 100)

// AI endpoint rate limit: 10 requests per minute
export const aiRateLimit = createRateLimit(60 * 1000, 10)

// Auth rate limit: 5 attempts per 15 minutes
export const authRateLimit = createRateLimit(15 * 60 * 1000, 5)

// Security headers
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.openai.com"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
})

// CORS configuration
export const corsOptions = {
  origin:
    process.env.NODE_ENV === "production"
      ? ["https://focusflow.app", "https://www.focusflow.app"]
      : ["http://localhost:3000", "http://127.0.0.1:3000"],
  credentials: true,
  optionsSuccessStatus: 200,
}

// Input validation middleware
export const validateInput = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body)
    if (error) {
      return res.status(400).json({
        error: "Invalid input",
        details: error.details.map((d: any) => d.message),
      })
    }
    next()
  }
}

// API key validation
export const validateApiKey = (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.headers["x-api-key"]
  const validApiKeys = process.env.VALID_API_KEYS?.split(",") || []

  if (!apiKey || !validApiKeys.includes(apiKey as string)) {
    return res.status(401).json({ error: "Invalid API key" })
  }

  next()
}

// Request logging
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now()

  res.on("finish", () => {
    const duration = Date.now() - start
    console.log(`${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`)
  })

  next()
}
