import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive/hive.dart';
import 'package:crypto/crypto.dart';
import '../models/task.dart';
import '../models/user.dart';

class AIService {
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const String _model = 'gpt-4';
  static const String _fastModel = 'gpt-3.5-turbo';

  // Multi-layered caching system
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static Box? _localCache;
  static final Map<String, dynamic> _memoryCache = {};

  // AI Agents for different tasks
  static const Map<String, String> _agents = {
    'taskBreakdown': 'ADHD Task Breakdown Specialist',
    'dailyPlanning': 'ADHD Daily Planning Coach',
    'reflection': 'ADHD Progress Reflection Analyst',
    'nudging': 'ADHD Motivation & Nudging Expert',
    'energyOptimization': 'ADHD Energy Management Advisor',
  };

  static Future<void> initialize() async {
    try {
      _localCache = await Hive.openBox('ai_cache');
    } catch (e) {
      print('Failed to initialize AI cache: $e');
    }
  }
  
  /// Enhanced task breakdown with multi-agent approach
  static Future<AIBreakdown?> getTaskBreakdown(
    String taskTitle,
    String? description, {
    User? userContext,
    int? energyLevel,
    List<String>? previousTasks,
    DateTime? deadline,
  }) async {
    try {
      // Check multi-layered cache
      final cacheKey = _generateAdvancedCacheKey(
        taskTitle,
        description,
        userContext?.id,
        energyLevel,
      );

      final cachedResult = await _getCachedResult(cacheKey);
      if (cachedResult != null) {
        return AIBreakdown.fromJson(cachedResult);
      }

      // Generate context-aware breakdown using specialized agent
      final prompt = _buildContextAwarePrompt(
        taskTitle,
        description,
        userContext: userContext,
        energyLevel: energyLevel,
        previousTasks: previousTasks,
        deadline: deadline,
      );

      final response = await _callOpenAI(
        prompt,
        agent: 'taskBreakdown',
        useAdvancedModel: true,
      );

      if (response != null) {
        final breakdown = _parseAdvancedAIResponse(response);
        // Cache with TTL based on context
        await _cacheResult(cacheKey, breakdown.toJson(), ttlHours: 24);
        return breakdown;
      }
    } catch (e) {
      print('AI Service Error: $e');
    }
    return null;
  }
  
  /// Generate adaptive daily plan using AI
  static Future<Map<String, dynamic>?> generateAdaptiveDailyPlan({
    required List<Task> availableTasks,
    required User userContext,
    required int energyLevel,
    required int availableMinutes,
    List<String>? calendarEvents,
  }) async {
    try {
      final cacheKey = _generateAdvancedCacheKey(
        'daily_plan_${DateTime.now().toIso8601String().split('T')[0]}',
        null,
        userContext.id,
        energyLevel,
      );

      final cachedResult = await _getCachedResult(cacheKey);
      if (cachedResult != null) {
        return cachedResult;
      }

      final prompt = _buildDailyPlanningPrompt(
        availableTasks,
        userContext,
        energyLevel,
        availableMinutes,
        calendarEvents,
      );

      final response = await _callOpenAI(
        prompt,
        agent: 'dailyPlanning',
        useAdvancedModel: true,
      );

      if (response != null) {
        final planData = jsonDecode(response);
        await _cacheResult(cacheKey, planData, ttlHours: 12);
        return planData;
      }
    } catch (e) {
      print('Daily planning AI error: $e');
    }
    return null;
  }

  /// Generate weekly reflection with insights
  static Future<Map<String, dynamic>?> generateWeeklyReflection({
    required String userId,
    required Map<String, dynamic> weekData,
  }) async {
    try {
      final cacheKey = _generateAdvancedCacheKey(
        'weekly_reflection',
        null,
        userId,
        null,
      );

      final prompt = _buildReflectionPrompt(weekData);

      final response = await _callOpenAI(
        prompt,
        agent: 'reflection',
        useAdvancedModel: true,
      );

      if (response != null) {
        final reflectionData = jsonDecode(response);
        await _cacheResult(cacheKey, reflectionData, ttlHours: 168); // 1 week
        return reflectionData;
      }
    } catch (e) {
      print('Weekly reflection AI error: $e');
    }
    return null;
  }

  /// Generate smart nudge based on context
  static Future<String?> generateSmartNudge({
    required User user,
    required Map<String, dynamic> context,
  }) async {
    try {
      final prompt = _buildNudgePrompt(user, context);

      final response = await _callOpenAI(
        prompt,
        agent: 'nudging',
        useAdvancedModel: false, // Use faster model for nudges
      );

      return response;
    } catch (e) {
      print('Smart nudge AI error: $e');
    }
    return null;
  }

  static String _buildContextAwarePrompt(
    String title,
    String? description, {
    User? userContext,
    int? energyLevel,
    List<String>? previousTasks,
    DateTime? deadline,
  }) {
    final agent = _agents['taskBreakdown']!;

    return '''
You are a $agent with deep expertise in ADHD task management.

TASK TO BREAK DOWN:
Title: "$title"
${description != null ? 'Description: "$description"' : ''}

USER CONTEXT:
${userContext != null ? '''
- ADHD Profile: ${userContext.onboarding?.adhdChallenges?.join(', ') ?? 'General ADHD support'}
- Preferred Work Style: ${userContext.preferences.focusMode}
- Current Energy Level: ${energyLevel ?? 'Unknown'}/5
''' : ''}

${previousTasks != null && previousTasks.isNotEmpty ? '''
RECENT COMPLETED TASKS (for pattern recognition):
${previousTasks.take(3).map((task) => '- $task').join('\n')}
''' : ''}

${deadline != null ? 'DEADLINE: ${deadline.toIso8601String()}' : ''}

INSTRUCTIONS:
Break this task into 3-7 concrete, actionable steps optimized for ADHD brains:

1. Consider the user's energy level and ADHD challenges
2. Make each step specific and achievable (avoid vague language)
3. Estimate realistic time for each step
4. Rate difficulty (1=very easy, 5=very hard)
5. Suggest optimal order based on energy and dopamine management
6. Include ADHD-friendly tips for each step
7. Identify potential obstacles and solutions

Respond in JSON format:
{
  "steps": [
    {
      "title": "Clear, actionable step",
      "description": "Detailed explanation",
      "estimatedMinutes": 15,
      "difficulty": 2,
      "energyRequired": "low|medium|high",
      "adhdTips": ["tip1", "tip2"],
      "potentialObstacles": ["obstacle1"],
      "solutions": ["solution1"]
    }
  ],
  "difficulty": 3,
  "suggestedOrder": [0, 1, 2, ...],
  "timeEstimates": [15, 30, 20, ...],
  "tips": ["tip1", "tip2", "tip3"]
}

Make steps specific and actionable. Avoid vague language like "plan" or "organize".
''';
  }

  static String _buildDailyPlanningPrompt(
    List<Task> availableTasks,
    User userContext,
    int energyLevel,
    int availableMinutes,
    List<String>? calendarEvents,
  ) {
    final agent = _agents['dailyPlanning']!;

    return '''
You are a $agent specializing in ADHD-optimized daily planning.

USER PROFILE:
- Energy Level: $energyLevel/5
- Available Time: $availableMinutes minutes
- ADHD Challenges: ${userContext.onboarding?.adhdChallenges?.join(', ') ?? 'General'}
- Preferred Focus Mode: ${userContext.preferences.focusMode}

AVAILABLE TASKS:
${availableTasks.map((task) => '''
- "${task.title}" (Priority: ${task.priority}, Est: ${task.estimatedMinutes}min)
  ${task.description.isNotEmpty ? 'Description: ${task.description}' : ''}
''').join('\n')}

${calendarEvents != null && calendarEvents.isNotEmpty ? '''
CALENDAR EVENTS:
${calendarEvents.map((event) => '- $event').join('\n')}
''' : ''}

INSTRUCTIONS:
Create an optimized daily plan considering:
1. Energy level and ADHD patterns
2. Task priorities and dependencies
3. Optimal timing for different task types
4. Break scheduling and energy management
5. Realistic time estimates with ADHD buffer time

Respond in JSON format:
{
  "todaysThree": ["taskId1", "taskId2", "taskId3"],
  "schedule": [
    {
      "time": "09:00",
      "activity": "High-energy task",
      "taskId": "task1",
      "duration": 45,
      "energyRequired": "high"
    }
  ],
  "energyManagement": {
    "peakHours": ["09:00-11:00"],
    "breakTimes": ["10:30", "14:00"],
    "lowEnergyTasks": ["taskId4", "taskId5"]
  },
  "adhdOptimizations": {
    "transitionTime": 5,
    "bufferTime": 15,
    "dopamineBoosts": ["reward1", "reward2"]
  }
}
''';
  }
  
  /// Enhanced OpenAI call with agent specialization
  static Future<String?> _callOpenAI(
    String prompt, {
    String? agent,
    bool useAdvancedModel = false,
  }) async {
    // This would use your OpenAI API key
    // For demo purposes, return mock data
    await Future.delayed(const Duration(seconds: 1));
    
    return '''
{
  "steps": [
    "Gather all necessary materials and documents",
    "Set up a distraction-free workspace",
    "Break the main task into smaller 15-minute chunks",
    "Complete the first chunk with a timer",
    "Take a 5-minute celebration break",
    "Continue with remaining chunks"
  ],
  "difficulty": 3,
  "suggestedOrder": [0, 1, 2, 3, 4, 5],
  "timeEstimates": [10, 5, 15, 25, 5, 30],
  "tips": [
    "Use body doubling - work alongside someone else",
    "Set a timer for each step to create urgency",
    "Reward yourself after each completed step"
  ]
}
''';
  }
  
  static AIBreakdown _parseAIResponse(String response) {
    final data = json.decode(response);
    return AIBreakdown(
      steps: List<String>.from(data['steps']),
      difficulty: data['difficulty'],
      suggestedOrder: List<int>.from(data['suggestedOrder']),
      timeEstimates: List<int>.from(data['timeEstimates']),
      tips: List<String>.from(data['tips']),
      cachedAt: DateTime.now(),
      model: _model,
    );
  }
  
  static String _generateCacheKey(String title, String? description) {
    return '${title}_${description ?? ''}'.hashCode.toString();
  }
  
  static Future<AIBreakdown?> _getCachedBreakdown(String cacheKey) async {
    try {
      final doc = await _firestore
          .collection('ai_cache')
          .doc(cacheKey)
          .get();
      
      if (doc.exists) {
        final data = doc.data()!;
        final cachedAt = (data['cachedAt'] as Timestamp).toDate();
        
        // Cache expires after 30 days
        if (DateTime.now().difference(cachedAt).inDays < 30) {
          return AIBreakdown.fromMap(data);
        }
      }
    } catch (e) {
      print('Cache retrieval error: $e');
    }
    return null;
  }
  
  static Future<void> _cacheBreakdown(String cacheKey, AIBreakdown breakdown) async {
    try {
      await _firestore
          .collection('ai_cache')
          .doc(cacheKey)
          .set(breakdown.toMap());
    } catch (e) {
      print('Cache storage error: $e');
    }
  }

  /// Generate advanced cache key with context
  static String _generateAdvancedCacheKey(
    String primary,
    String? secondary,
    String? userId,
    int? energyLevel,
  ) {
    final input = '$primary${secondary ?? ''}${userId ?? ''}${energyLevel ?? ''}';
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 16);
  }

  /// Multi-layered cache retrieval
  static Future<Map<String, dynamic>?> _getCachedResult(String cacheKey) async {
    // 1. Check memory cache first (fastest)
    if (_memoryCache.containsKey(cacheKey)) {
      final cached = _memoryCache[cacheKey];
      if (cached['expiry'] > DateTime.now().millisecondsSinceEpoch) {
        return cached['data'];
      } else {
        _memoryCache.remove(cacheKey);
      }
    }

    // 2. Check local cache (Hive)
    if (_localCache != null) {
      try {
        final cached = _localCache!.get(cacheKey);
        if (cached != null) {
          final data = Map<String, dynamic>.from(cached);
          if (data['expiry'] > DateTime.now().millisecondsSinceEpoch) {
            // Promote to memory cache
            _memoryCache[cacheKey] = data;
            return data['data'];
          } else {
            await _localCache!.delete(cacheKey);
          }
        }
      } catch (e) {
        print('Local cache error: $e');
      }
    }

    return null;
  }

  /// Multi-layered cache storage
  static Future<void> _cacheResult(
    String cacheKey,
    Map<String, dynamic> data, {
    int ttlHours = 24,
  }) async {
    final expiry = DateTime.now()
        .add(Duration(hours: ttlHours))
        .millisecondsSinceEpoch;

    final cacheData = {
      'data': data,
      'expiry': expiry,
    };

    // Store in memory cache
    _memoryCache[cacheKey] = cacheData;

    // Store in local cache
    if (_localCache != null) {
      try {
        await _localCache!.put(cacheKey, cacheData);
      } catch (e) {
        print('Local cache storage error: $e');
      }
    }
  }

  /// Parse advanced AI response with error handling
  static AIBreakdown _parseAdvancedAIResponse(String response) {
    try {
      final data = jsonDecode(response);
      return AIBreakdown.fromJson(data);
    } catch (e) {
      print('AI response parsing error: $e');
      // Return fallback breakdown
      return AIBreakdown(
        steps: ['Break this task into smaller steps'],
        difficulty: 3,
        estimatedMinutes: 30,
        tips: ['Take breaks every 25 minutes', 'Start with the easiest step'],
      );
    }
  }
}
