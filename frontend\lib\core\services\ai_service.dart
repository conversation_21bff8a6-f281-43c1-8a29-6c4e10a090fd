import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/task.dart';

class AIService {
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const String _model = 'gpt-4';
  
  // Cache AI responses to reduce costs
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  static Future<AIBreakdown?> getTaskBreakdown(String taskTitle, String? description) async {
    try {
      // Check cache first
      final cacheKey = _generateCacheKey(taskTitle, description);
      final cachedResult = await _getCachedBreakdown(cacheKey);
      if (cachedResult != null) {
        return cachedResult;
      }
      
      // Generate new breakdown
      final prompt = _buildADHDPrompt(taskTitle, description);
      final response = await _callOpenAI(prompt);
      
      if (response != null) {
        final breakdown = _parseAIResponse(response);
        // Cache the result
        await _cacheBreakdown(cacheKey, breakdown);
        return breakdown;
      }
    } catch (e) {
      print('AI Service Error: $e');
    }
    return null;
  }
  
  static String _buildADHDPrompt(String title, String? description) {
    return '''
You are an ADHD coach helping break down tasks. For the task "$title"${description != null ? ' - $description' : ''}, provide:

1. Break it into 3-7 concrete, actionable steps
2. Rate difficulty 1-5 (1=very easy, 5=very hard)
3. Suggest order based on energy levels (high energy first)
4. Estimate time for each step in minutes
5. Give 2-3 ADHD-friendly tips (dopamine hits, body doubling, etc.)

Respond in JSON format:
{
  "steps": ["step1", "step2", ...],
  "difficulty": 3,
  "suggestedOrder": [0, 1, 2, ...],
  "timeEstimates": [15, 30, 20, ...],
  "tips": ["tip1", "tip2", "tip3"]
}

Make steps specific and actionable. Avoid vague language like "plan" or "organize".
''';
  }
  
  static Future<String?> _callOpenAI(String prompt) async {
    // This would use your OpenAI API key
    // For demo purposes, return mock data
    await Future.delayed(const Duration(seconds: 1));
    
    return '''
{
  "steps": [
    "Gather all necessary materials and documents",
    "Set up a distraction-free workspace",
    "Break the main task into smaller 15-minute chunks",
    "Complete the first chunk with a timer",
    "Take a 5-minute celebration break",
    "Continue with remaining chunks"
  ],
  "difficulty": 3,
  "suggestedOrder": [0, 1, 2, 3, 4, 5],
  "timeEstimates": [10, 5, 15, 25, 5, 30],
  "tips": [
    "Use body doubling - work alongside someone else",
    "Set a timer for each step to create urgency",
    "Reward yourself after each completed step"
  ]
}
''';
  }
  
  static AIBreakdown _parseAIResponse(String response) {
    final data = json.decode(response);
    return AIBreakdown(
      steps: List<String>.from(data['steps']),
      difficulty: data['difficulty'],
      suggestedOrder: List<int>.from(data['suggestedOrder']),
      timeEstimates: List<int>.from(data['timeEstimates']),
      tips: List<String>.from(data['tips']),
      cachedAt: DateTime.now(),
      model: _model,
    );
  }
  
  static String _generateCacheKey(String title, String? description) {
    return '${title}_${description ?? ''}'.hashCode.toString();
  }
  
  static Future<AIBreakdown?> _getCachedBreakdown(String cacheKey) async {
    try {
      final doc = await _firestore
          .collection('ai_cache')
          .doc(cacheKey)
          .get();
      
      if (doc.exists) {
        final data = doc.data()!;
        final cachedAt = (data['cachedAt'] as Timestamp).toDate();
        
        // Cache expires after 30 days
        if (DateTime.now().difference(cachedAt).inDays < 30) {
          return AIBreakdown.fromMap(data);
        }
      }
    } catch (e) {
      print('Cache retrieval error: $e');
    }
    return null;
  }
  
  static Future<void> _cacheBreakdown(String cacheKey, AIBreakdown breakdown) async {
    try {
      await _firestore
          .collection('ai_cache')
          .doc(cacheKey)
          .set(breakdown.toMap());
    } catch (e) {
      print('Cache storage error: $e');
    }
  }
}
