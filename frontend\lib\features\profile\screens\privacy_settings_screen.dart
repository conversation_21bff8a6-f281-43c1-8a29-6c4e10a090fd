import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../shared/widgets/calm_card.dart';
import '../../../shared/widgets/adhd_button.dart';
import '../../../shared/providers/auth_provider.dart';
import '../../../core/services/privacy_service.dart';
import '../../../core/services/encryption_service.dart';

class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  Map<String, dynamic> _privacySettings = {};
  Map<String, bool> _permissions = {};
  bool _isLoading = true;
  bool _isExporting = false;
  bool _isDeleting = false;

  @override
  void initState() {
    super.initState();
    _loadPrivacyData();
  }

  Future<void> _loadPrivacyData() async {
    setState(() => _isLoading = true);
    
    try {
      final settings = await PrivacyService.getPrivacySettings();
      final permissions = await PrivacyService.checkPrivacyPermissions();
      
      setState(() {
        _privacySettings = settings;
        _permissions = permissions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load privacy settings: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        title: const Text('Privacy & Security'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Privacy overview
                  _buildPrivacyOverview(context),
                  const SizedBox(height: 24),
                  
                  // Data collection settings
                  _buildDataCollectionSettings(context),
                  const SizedBox(height: 24),
                  
                  // Permissions
                  _buildPermissionsSection(context),
                  const SizedBox(height: 24),
                  
                  // Data management
                  _buildDataManagementSection(context),
                  const SizedBox(height: 24),
                  
                  // Account actions
                  _buildAccountActionsSection(context),
                  const SizedBox(height: 100),
                ],
              ),
            ),
    );
  }

  Widget _buildPrivacyOverview(BuildContext context) {
    final theme = Theme.of(context);
    
    return CalmCard(
      backgroundColor: theme.colorScheme.primaryContainer.withOpacity(0.3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: theme.colorScheme.primary,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your Privacy Matters',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'We believe in transparency and giving you control over your data.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface.withOpacity(0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lock,
                  color: theme.colorScheme.secondary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'All sensitive data is encrypted locally on your device',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.secondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataCollectionSettings(BuildContext context) {
    final theme = Theme.of(context);
    
    return CalmCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Data Collection',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildSettingToggle(
            'Analytics',
            'Help improve the app by sharing anonymous usage data',
            _privacySettings['analytics'] ?? true,
            (value) => _updateSetting('analytics', value),
          ),
          _buildSettingToggle(
            'Crash Reporting',
            'Automatically send crash reports to help fix bugs',
            _privacySettings['crashReporting'] ?? true,
            (value) => _updateSetting('crashReporting', value),
          ),
          _buildSettingToggle(
            'Personalized Ads',
            'Show ads based on your interests (when applicable)',
            _privacySettings['personalizedAds'] ?? false,
            (value) => _updateSetting('personalizedAds', value),
          ),
          _buildSettingToggle(
            'Data Sharing',
            'Share anonymized data for ADHD research',
            _privacySettings['dataSharing'] ?? false,
            (value) => _updateSetting('dataSharing', value),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return CalmCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'App Permissions',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          ..._permissions.entries.map((entry) => _buildPermissionItem(
            context,
            entry.key,
            entry.value,
          )),
        ],
      ),
    );
  }

  Widget _buildDataManagementSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return CalmCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Data Management',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          ADHDButton(
            text: _isExporting ? 'Exporting...' : 'Export My Data',
            icon: Icons.download,
            type: ADHDButtonType.secondary,
            isLoading: _isExporting,
            onPressed: _isExporting ? null : _exportData,
          ),
          const SizedBox(height: 12),
          ADHDButton(
            text: 'Clear Local Cache',
            icon: Icons.clear_all,
            type: ADHDButtonType.outline,
            onPressed: _clearLocalData,
          ),
        ],
      ),
    );
  }

  Widget _buildAccountActionsSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return CalmCard(
      backgroundColor: theme.colorScheme.errorContainer.withOpacity(0.1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Actions',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'These actions cannot be undone. Please proceed with caution.',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),
          ADHDButton(
            text: 'Anonymize My Data',
            icon: Icons.person_off,
            type: ADHDButtonType.outline,
            onPressed: _anonymizeData,
          ),
          const SizedBox(height: 12),
          ADHDButton(
            text: _isDeleting ? 'Deleting...' : 'Delete Account',
            icon: Icons.delete_forever,
            type: ADHDButtonType.destructive,
            isLoading: _isDeleting,
            onPressed: _isDeleting ? null : _deleteAccount,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingToggle(
    String title,
    String description,
    bool value,
    Function(bool) onChanged,
  ) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionItem(BuildContext context, String permission, bool granted) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            granted ? Icons.check_circle : Icons.cancel,
            color: granted ? Colors.green : Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              permission.toUpperCase(),
              style: theme.textTheme.bodyMedium,
            ),
          ),
          if (!granted)
            TextButton(
              onPressed: () => _requestPermission(permission),
              child: const Text('Grant'),
            ),
        ],
      ),
    );
  }

  Future<void> _updateSetting(String key, bool value) async {
    final newSettings = Map<String, dynamic>.from(_privacySettings);
    newSettings[key] = value;
    
    final success = await PrivacyService.updatePrivacySettings(newSettings);
    if (success) {
      setState(() {
        _privacySettings = newSettings;
      });
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to update setting')),
      );
    }
  }

  Future<void> _requestPermission(String permission) async {
    // Implementation would depend on the specific permission
    await _loadPrivacyData(); // Refresh permissions
  }

  Future<void> _exportData() async {
    setState(() => _isExporting = true);
    
    try {
      final filePath = await PrivacyService.exportUserData();
      if (filePath != null) {
        final shared = await PrivacyService.shareExportedData(filePath);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(shared 
                  ? 'Data exported successfully!' 
                  : 'Data exported to: $filePath'),
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to export data')),
        );
      }
    } finally {
      setState(() => _isExporting = false);
    }
  }

  Future<void> _clearLocalData() async {
    final confirmed = await _showConfirmationDialog(
      'Clear Local Data',
      'This will clear all locally cached data. Your account data will remain safe.',
    );
    
    if (confirmed) {
      await PrivacyService.clearLocalData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Local data cleared')),
        );
      }
    }
  }

  Future<void> _anonymizeData() async {
    final confirmed = await _showConfirmationDialog(
      'Anonymize Data',
      'This will remove all personally identifiable information from your data. This action cannot be undone.',
    );
    
    if (confirmed) {
      final success = await PrivacyService.anonymizeUserData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success 
                ? 'Data anonymized successfully' 
                : 'Failed to anonymize data'),
          ),
        );
      }
    }
  }

  Future<void> _deleteAccount() async {
    final confirmed = await _showConfirmationDialog(
      'Delete Account',
      'This will permanently delete your account and all associated data. This action cannot be undone.',
      isDestructive: true,
    );
    
    if (confirmed) {
      setState(() => _isDeleting = true);
      
      try {
        final success = await PrivacyService.deleteAllUserData();
        if (success) {
          // Clear encryption keys
          await EncryptionService.clearEncryptionKeys();
          
          if (mounted) {
            // Sign out and navigate to login
            await context.read<AuthProvider>().signOut();
            Navigator.of(context).pushNamedAndRemoveUntil(
              '/auth/login',
              (route) => false,
            );
          }
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to delete account')),
          );
        }
      } finally {
        setState(() => _isDeleting = false);
      }
    }
  }

  Future<bool> _showConfirmationDialog(
    String title,
    String content, {
    bool isDestructive = false,
  }) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: isDestructive
                ? ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.error,
                  )
                : null,
            child: Text(isDestructive ? 'Delete' : 'Confirm'),
          ),
        ],
      ),
    ) ?? false;
  }
}
