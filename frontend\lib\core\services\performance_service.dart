import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'analytics_service.dart';

class PerformanceService {
  static final FirebasePerformance _performance = FirebasePerformance.instance;
  static final Map<String, Trace> _activeTraces = {};
  static final Map<String, DateTime> _screenLoadTimes = {};
  static final List<PerformanceMetric> _customMetrics = [];
  
  static bool _isInitialized = false;
  static Map<String, dynamic>? _deviceInfo;

  /// Initialize performance monitoring
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Enable performance monitoring
      await _performance.setPerformanceCollectionEnabled(!kDebugMode);
      
      // Collect device information
      await _collectDeviceInfo();
      
      // Start app launch trace
      startTrace('app_launch');
      
      _isInitialized = true;
      debugPrint('Performance monitoring initialized');
    } catch (e) {
      debugPrint('Performance monitoring initialization error: $e');
    }
  }

  /// Collect device information for performance context
  static Future<void> _collectDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        _deviceInfo = {
          'platform': 'android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'androidVersion': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'totalMemory': androidInfo.systemFeatures.length,
          'appVersion': packageInfo.version,
          'buildNumber': packageInfo.buildNumber,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        _deviceInfo = {
          'platform': 'ios',
          'model': iosInfo.model,
          'systemVersion': iosInfo.systemVersion,
          'name': iosInfo.name,
          'appVersion': packageInfo.version,
          'buildNumber': packageInfo.buildNumber,
        };
      }
    } catch (e) {
      debugPrint('Device info collection error: $e');
    }
  }

  /// Start a performance trace
  static void startTrace(String traceName) {
    try {
      if (_activeTraces.containsKey(traceName)) {
        debugPrint('Trace $traceName already active');
        return;
      }
      
      final trace = _performance.newTrace(traceName);
      trace.start();
      _activeTraces[traceName] = trace;
      
      debugPrint('Started trace: $traceName');
    } catch (e) {
      debugPrint('Start trace error: $e');
    }
  }

  /// Stop a performance trace
  static void stopTrace(String traceName, {Map<String, String>? attributes}) {
    try {
      final trace = _activeTraces.remove(traceName);
      if (trace == null) {
        debugPrint('Trace $traceName not found');
        return;
      }
      
      // Add custom attributes
      if (attributes != null) {
        for (final entry in attributes.entries) {
          trace.putAttribute(entry.key, entry.value);
        }
      }
      
      // Add device info attributes
      if (_deviceInfo != null) {
        trace.putAttribute('device_platform', _deviceInfo!['platform'] ?? 'unknown');
        trace.putAttribute('app_version', _deviceInfo!['appVersion'] ?? 'unknown');
      }
      
      trace.stop();
      debugPrint('Stopped trace: $traceName');
    } catch (e) {
      debugPrint('Stop trace error: $e');
    }
  }

  /// Record a custom metric
  static void recordMetric(String metricName, double value, {String? unit}) {
    try {
      final metric = PerformanceMetric(
        name: metricName,
        value: value,
        unit: unit ?? 'ms',
        timestamp: DateTime.now(),
      );
      
      _customMetrics.add(metric);
      
      // Log to analytics
      AnalyticsService.logEvent('performance_metric', parameters: {
        'metric_name': metricName,
        'metric_value': value,
        'metric_unit': unit ?? 'ms',
      });
      
      debugPrint('Recorded metric: $metricName = $value ${unit ?? 'ms'}');
    } catch (e) {
      debugPrint('Record metric error: $e');
    }
  }

  /// Track screen load time
  static void startScreenLoad(String screenName) {
    _screenLoadTimes[screenName] = DateTime.now();
    startTrace('screen_load_$screenName');
  }

  /// Complete screen load tracking
  static void completeScreenLoad(String screenName) {
    final startTime = _screenLoadTimes.remove(screenName);
    if (startTime != null) {
      final loadTime = DateTime.now().difference(startTime).inMilliseconds;
      recordMetric('screen_load_time_$screenName', loadTime.toDouble());
      
      stopTrace('screen_load_$screenName', attributes: {
        'screen_name': screenName,
        'load_time_ms': loadTime.toString(),
      });
    }
  }

  /// Track API call performance
  static Future<T> trackApiCall<T>(
    String apiName,
    Future<T> Function() apiCall,
  ) async {
    final traceName = 'api_call_$apiName';
    startTrace(traceName);
    
    final startTime = DateTime.now();
    
    try {
      final result = await apiCall();
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      
      stopTrace(traceName, attributes: {
        'api_name': apiName,
        'success': 'true',
        'duration_ms': duration.toString(),
      });
      
      recordMetric('api_call_duration_$apiName', duration.toDouble());
      
      return result;
    } catch (e) {
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      
      stopTrace(traceName, attributes: {
        'api_name': apiName,
        'success': 'false',
        'error': e.toString(),
        'duration_ms': duration.toString(),
      });
      
      recordMetric('api_call_error_$apiName', duration.toDouble());
      
      rethrow;
    }
  }

  /// Track database operation performance
  static Future<T> trackDatabaseOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final traceName = 'db_operation_$operationName';
    startTrace(traceName);
    
    final startTime = DateTime.now();
    
    try {
      final result = await operation();
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      
      stopTrace(traceName, attributes: {
        'operation_name': operationName,
        'success': 'true',
        'duration_ms': duration.toString(),
      });
      
      recordMetric('db_operation_duration_$operationName', duration.toDouble());
      
      return result;
    } catch (e) {
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      
      stopTrace(traceName, attributes: {
        'operation_name': operationName,
        'success': 'false',
        'error': e.toString(),
        'duration_ms': duration.toString(),
      });
      
      recordMetric('db_operation_error_$operationName', duration.toDouble());
      
      rethrow;
    }
  }

  /// Monitor memory usage
  static Future<void> recordMemoryUsage(String context) async {
    try {
      // Get memory info from platform
      const platform = MethodChannel('com.focusflow.performance');
      final memoryInfo = await platform.invokeMethod('getMemoryInfo');
      
      if (memoryInfo != null) {
        final usedMemory = memoryInfo['usedMemory'] as double? ?? 0.0;
        final totalMemory = memoryInfo['totalMemory'] as double? ?? 0.0;
        final memoryPercentage = totalMemory > 0 ? (usedMemory / totalMemory) * 100 : 0.0;
        
        recordMetric('memory_usage_$context', usedMemory, unit: 'MB');
        recordMetric('memory_percentage_$context', memoryPercentage, unit: '%');
        
        // Alert if memory usage is high
        if (memoryPercentage > 80) {
          AnalyticsService.logEvent('high_memory_usage', parameters: {
            'context': context,
            'memory_percentage': memoryPercentage,
            'used_memory_mb': usedMemory,
          });
        }
      }
    } catch (e) {
      debugPrint('Memory monitoring error: $e');
    }
  }

  /// Track frame rendering performance
  static void trackFramePerformance() {
    if (!kDebugMode) return;
    
    WidgetsBinding.instance.addTimingsCallback((timings) {
      for (final timing in timings) {
        final frameTime = timing.totalSpan.inMicroseconds / 1000.0; // Convert to ms
        
        if (frameTime > 16.67) { // 60 FPS threshold
          recordMetric('slow_frame', frameTime);
          
          if (frameTime > 33.33) { // 30 FPS threshold
            AnalyticsService.logEvent('very_slow_frame', parameters: {
              'frame_time_ms': frameTime,
              'build_duration_ms': timing.buildDuration.inMicroseconds / 1000.0,
              'raster_duration_ms': timing.rasterDuration.inMicroseconds / 1000.0,
            });
          }
        }
      }
    });
  }

  /// Get performance summary
  static Map<String, dynamic> getPerformanceSummary() {
    final now = DateTime.now();
    final recentMetrics = _customMetrics
        .where((metric) => now.difference(metric.timestamp).inMinutes < 60)
        .toList();
    
    return {
      'total_metrics': _customMetrics.length,
      'recent_metrics': recentMetrics.length,
      'active_traces': _activeTraces.keys.toList(),
      'device_info': _deviceInfo,
      'is_initialized': _isInitialized,
      'recent_metric_summary': _summarizeMetrics(recentMetrics),
    };
  }

  /// Summarize metrics by category
  static Map<String, dynamic> _summarizeMetrics(List<PerformanceMetric> metrics) {
    final summary = <String, List<double>>{};
    
    for (final metric in metrics) {
      final category = metric.name.split('_').first;
      summary.putIfAbsent(category, () => []).add(metric.value);
    }
    
    final result = <String, dynamic>{};
    for (final entry in summary.entries) {
      final values = entry.value;
      result[entry.key] = {
        'count': values.length,
        'average': values.reduce((a, b) => a + b) / values.length,
        'min': values.reduce((a, b) => a < b ? a : b),
        'max': values.reduce((a, b) => a > b ? a : b),
      };
    }
    
    return result;
  }

  /// Clear old metrics to prevent memory leaks
  static void cleanupOldMetrics() {
    final cutoff = DateTime.now().subtract(const Duration(hours: 24));
    _customMetrics.removeWhere((metric) => metric.timestamp.isBefore(cutoff));
  }

  /// Force stop all active traces (for app termination)
  static void stopAllTraces() {
    for (final traceName in _activeTraces.keys.toList()) {
      stopTrace(traceName, attributes: {'forced_stop': 'true'});
    }
  }
}

class PerformanceMetric {
  final String name;
  final double value;
  final String unit;
  final DateTime timestamp;

  PerformanceMetric({
    required this.name,
    required this.value,
    required this.unit,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'name': name,
    'value': value,
    'unit': unit,
    'timestamp': timestamp.toIso8601String(),
  };
}
