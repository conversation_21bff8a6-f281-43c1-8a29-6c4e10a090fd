import 'package:flutter/material.dart';

import '../../../shared/widgets/calm_card.dart';
import '../../../shared/widgets/adhd_button.dart';

class PricingCards extends StatelessWidget {
  final bool isLoading;
  final VoidCallback onMonthlySelected;
  final VoidCallback onYearlySelected;

  const PricingCards({
    super.key,
    required this.isLoading,
    required this.onMonthlySelected,
    required this.onYearlySelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Yearly plan (recommended)
        _buildPricingCard(
          context,
          title: 'Yearly Plan',
          subtitle: 'Most Popular',
          price: '\$39.99',
          period: 'per year',
          savings: 'Save 52%',
          features: [
            'Everything in Pro',
            '2 months free',
            'Priority support',
            'Early access to new features',
          ],
          isRecommended: true,
          onPressed: isLoading ? null : onYearlySelected,
        ),
        
        const SizedBox(height: 16),
        
        // Monthly plan
        _buildPricingCard(
          context,
          title: 'Monthly Plan',
          subtitle: 'Flexible',
          price: '\$6.99',
          period: 'per month',
          features: [
            'All Pro features',
            'Cancel anytime',
            'No commitment',
          ],
          isRecommended: false,
          onPressed: isLoading ? null : onMonthlySelected,
        ),
      ],
    );
  }

  Widget _buildPricingCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required String price,
    required String period,
    String? savings,
    required List<String> features,
    required bool isRecommended,
    required VoidCallback? onPressed,
  }) {
    final theme = Theme.of(context);
    
    return CalmCard(
      backgroundColor: isRecommended 
          ? theme.colorScheme.primaryContainer.withOpacity(0.3)
          : theme.colorScheme.surface,
      showBorder: isRecommended,
      borderColor: theme.colorScheme.primary,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          title,
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (isRecommended) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'RECOMMENDED',
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: theme.colorScheme.onPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    Text(
                      subtitle,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              if (savings != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.secondary,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    savings,
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: theme.colorScheme.onSecondary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Price
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                price,
                style: theme.textTheme.displayMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(width: 8),
              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text(
                  period,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Features
          ...features.map((feature) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  size: 20,
                  color: theme.colorScheme.secondary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    feature,
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          )),
          
          const SizedBox(height: 20),
          
          // CTA Button
          ADHDButton(
            text: isLoading ? 'Processing...' : 'Start Free Trial',
            type: isRecommended ? ADHDButtonType.primary : ADHDButtonType.outline,
            isLoading: isLoading,
            onPressed: onPressed,
          ),
          
          const SizedBox(height: 8),
          
          // Trial info
          Text(
            '7-day free trial • Cancel anytime',
            textAlign: TextAlign.center,
            style: theme.textTheme.labelMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }
}
