import { OpenAI } from "openai"
import { getFirestore } from "firebase-admin/firestore"
import { createHash } from "crypto"
import type { Task, User, WeeklyReflection } from "../models/types"

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

const db = getFirestore()

export class AIService {
  private static readonly CACHE_DURATION_DAYS = 7
  private static readonly DEFAULT_MODEL = "gpt-4o-mini"

  /**
   * Generate AI task breakdown with intelligent caching
   */
  static async generateTaskBreakdown(task: Partial<Task>, user: User): Promise<Task["aiBreakdown"]> {
    const prompt = this.buildTaskBreakdownPrompt(task, user)
    const cacheKey = this.generateCacheKey(prompt, this.DEFAULT_MODEL)

    // Check cache first
    const cached = await this.getCachedResponse(cacheKey)
    if (cached) {
      await this.updateCacheHitCount(cacheKey)
      return JSON.parse(cached.response)
    }

    try {
      const completion = await openai.chat.completions.create({
        model: this.DEFAULT_MODEL,
        messages: [
          {
            role: "system",
            content: `You are an ADHD-specialized task breakdown assistant. Your job is to break down tasks into manageable, dopamine-friendly steps that reduce overwhelm and increase completion likelihood.

ADHD-Friendly Principles:
- Break tasks into 3-7 concrete steps (never more than 7)
- Each step should take 5-25 minutes
- Use action-oriented language ("Write outline" not "Think about structure")
- Include micro-rewards between steps
- Consider energy levels and attention spans
- Provide encouraging, non-judgmental tone

Response format: JSON only, no markdown
{
  "steps": ["Step 1", "Step 2", ...],
  "difficulty": 1-5,
  "suggestedOrder": [0, 1, 2, ...],
  "timeEstimates": [15, 20, 10, ...],
  "tips": ["Helpful tip 1", "Helpful tip 2"]
}`,
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.7,
        max_tokens: 800,
      })

      const response = completion.choices[0]?.message?.content
      if (!response) {
        throw new Error("No response from OpenAI")
      }

      // Parse and validate response
      const breakdown = JSON.parse(response)

      // Cache the response
      await this.cacheResponse(cacheKey, prompt, response, this.DEFAULT_MODEL)

      return {
        ...breakdown,
        cachedAt: new Date(),
        model: this.DEFAULT_MODEL,
      }
    } catch (error) {
      console.error("AI task breakdown failed:", error)
      // Return fallback breakdown
      return this.generateFallbackBreakdown(task)
    }
  }

  /**
   * Generate weekly reflection with AI insights
   */
  static async generateWeeklyReflection(
    userId: string,
    weekData: {
      completedTasks: Task[]
      focusSessions: any[]
      dailyPlans: any[]
    },
  ): Promise<WeeklyReflection["aiAnalysis"]> {
    const prompt = this.buildWeeklyReflectionPrompt(weekData)
    const cacheKey = this.generateCacheKey(prompt + userId, this.DEFAULT_MODEL)

    // Check cache (shorter duration for personalized content)
    const cached = await this.getCachedResponse(cacheKey, 1) // 1 day cache
    if (cached) {
      return JSON.parse(cached.response)
    }

    try {
      const completion = await openai.chat.completions.create({
        model: this.DEFAULT_MODEL,
        messages: [
          {
            role: "system",
            content: `You are an ADHD-specialized productivity coach providing weekly reflection insights. Focus on patterns, celebrate wins, and provide actionable suggestions.

Guidelines:
- Be encouraging and strength-focused
- Identify specific patterns in behavior/productivity
- Suggest 2-3 concrete improvements for next week
- Acknowledge ADHD challenges without being patronizing
- Keep suggestions simple and achievable

Response format: JSON only
{
  "patterns": ["Pattern 1", "Pattern 2"],
  "suggestions": ["Suggestion 1", "Suggestion 2"],
  "encouragement": "Encouraging message",
  "nextWeekFocus": ["Focus area 1", "Focus area 2"]
}`,
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.8,
        max_tokens: 600,
      })

      const response = completion.choices[0]?.message?.content
      if (!response) {
        throw new Error("No response from OpenAI")
      }

      const analysis = JSON.parse(response)

      // Cache with shorter duration for personalized content
      await this.cacheResponse(cacheKey, prompt, response, this.DEFAULT_MODEL, 1)

      return {
        ...analysis,
        generatedAt: new Date(),
      }
    } catch (error) {
      console.error("AI weekly reflection failed:", error)
      return this.generateFallbackReflection()
    }
  }

  /**
   * Generate smart nudges based on user behavior
   */
  static async generateSmartNudge(
    user: User,
    context: {
      timeOfDay: number // 0-23
      lastActivity: string
      currentStreak: number
      energyLevel?: number
    },
  ): Promise<string> {
    const prompt = `Generate a gentle, encouraging nudge for an ADHD user.
    
Context:
- Time: ${context.timeOfDay}:00
- Last activity: ${context.lastActivity}
- Current streak: ${context.currentStreak} days
- Energy level: ${context.energyLevel || "unknown"}/5

The nudge should be:
- Brief (1-2 sentences)
- Encouraging, not pushy
- Contextually appropriate for time of day
- ADHD-friendly (understanding of executive function challenges)

Just return the nudge text, no JSON.`

    try {
      const completion = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.9,
        max_tokens: 100,
      })

      return completion.choices[0]?.message?.content || "You've got this! Even small steps count. 🌟"
    } catch (error) {
      console.error("Smart nudge generation failed:", error)
      return "Take it one step at a time. You're doing great! 💪"
    }
  }

  // Private helper methods
  private static buildTaskBreakdownPrompt(task: Partial<Task>, user: User): string {
    return `Break down this task for someone with ADHD:

Task: "${task.title}"
Description: "${task.description || "No additional details"}"
Estimated time: ${task.estimatedMinutes || "Unknown"} minutes
Priority: ${task.priority || "Medium"}

User context:
- ADHD assessment score: ${user.onboarding?.adhdAssessmentScore || "Unknown"}/10
- Primary challenges: ${user.onboarding?.primaryChallenges?.join(", ") || "General ADHD challenges"}
- Preferred focus duration: ${user.preferences.defaultFocusMinutes} minutes
- Current streak: ${user.stats.currentStreak} days

Please break this down into manageable steps that work well for ADHD brains.`
  }

  private static buildWeeklyReflectionPrompt(weekData: any): string {
    return `Analyze this week's productivity data for an ADHD user:

Completed Tasks: ${weekData.completedTasks.length}
Focus Sessions: ${weekData.focusSessions.length} (${weekData.focusSessions.reduce((sum: number, s: any) => sum + s.duration, 0)} total minutes)
Daily Plans Created: ${weekData.dailyPlans.length}

Task completion patterns:
${weekData.completedTasks.map((t: Task) => `- ${t.title} (${t.actualMinutes}min)`).join("\n")}

Focus session patterns:
${weekData.focusSessions.map((s: any) => `- ${s.type}: ${s.duration}min, ${s.interruptions} interruptions`).join("\n")}

Provide insights and suggestions for next week.`
  }

  private static generateCacheKey(prompt: string, model: string): string {
    return createHash("sha256")
      .update(prompt + model)
      .digest("hex")
  }

  private static async getCachedResponse(
    cacheKey: string,
    maxAgeDays: number = this.CACHE_DURATION_DAYS,
  ): Promise<any | null> {
    try {
      const doc = await db.collection("aiCache").doc(cacheKey).get()
      if (!doc.exists) return null

      const cached = doc.data()
      const now = new Date()
      const cacheAge = now.getTime() - cached.createdAt.toDate().getTime()
      const maxAge = maxAgeDays * 24 * 60 * 60 * 1000

      if (cacheAge > maxAge) {
        // Cache expired, delete it
        await doc.ref.delete()
        return null
      }

      return cached
    } catch (error) {
      console.error("Cache retrieval failed:", error)
      return null
    }
  }

  private static async cacheResponse(
    cacheKey: string,
    prompt: string,
    response: string,
    model: string,
    durationDays: number = this.CACHE_DURATION_DAYS,
  ): Promise<void> {
    try {
      const now = new Date()
      const expiresAt = new Date(now.getTime() + durationDays * 24 * 60 * 60 * 1000)

      await db.collection("aiCache").doc(cacheKey).set({
        prompt,
        response,
        model,
        createdAt: now,
        expiresAt,
        hitCount: 0,
        lastUsed: now,
      })
    } catch (error) {
      console.error("Cache storage failed:", error)
      // Don't throw - caching failure shouldn't break the main flow
    }
  }

  private static async updateCacheHitCount(cacheKey: string): Promise<void> {
    try {
      await db
        .collection("aiCache")
        .doc(cacheKey)
        .update({
          hitCount: db.FieldValue.increment(1),
          lastUsed: new Date(),
        })
    } catch (error) {
      console.error("Cache hit count update failed:", error)
    }
  }

  private static generateFallbackBreakdown(task: Partial<Task>): Task["aiBreakdown"] {
    return {
      steps: [
        "Gather all materials needed",
        "Break the task into smaller parts",
        "Complete the first part",
        "Take a short break",
        "Complete remaining parts",
        "Review and finalize",
      ],
      difficulty: 3,
      suggestedOrder: [0, 1, 2, 3, 4, 5],
      timeEstimates: [5, 10, 15, 5, 20, 10],
      tips: ["Set a timer for each step", "Celebrate completing each step", "It's okay to take breaks between steps"],
      cachedAt: new Date(),
      model: "fallback",
    }
  }

  private static generateFallbackReflection(): WeeklyReflection["aiAnalysis"] {
    return {
      patterns: ["You're building consistent habits", "Focus sessions are becoming more regular"],
      suggestions: [
        "Try scheduling your most important task for your peak energy time",
        "Consider breaking larger tasks into smaller steps",
      ],
      encouragement: "Every step forward counts, no matter how small. You're making progress!",
      nextWeekFocus: ["Maintain your current momentum", "Experiment with one new productivity technique"],
      generatedAt: new Date(),
    }
  }
}
