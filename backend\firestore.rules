rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow reading basic profile info for body-doubling features
      allow read: if request.auth != null && 
        resource.data.keys().hasOnly(['id', 'displayName', 'stats']);
    }
    
    // Tasks are private to each user
    match /tasks/{taskId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // Daily plans are private to each user
    match /dailyPlans/{userId}/plans/{planId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Focus sessions are private to each user
    match /focusSessions/{sessionId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // Weekly reflections are private to each user
    match /weeklyReflections/{reflectionId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    // AI cache is system-managed (no direct user access)
    match /aiCache/{cacheId} {
      allow read, write: if false; // Only accessible via Cloud Functions
    }
    
    // Body-doubling rooms - users can read active rooms and join/leave
    match /bodyDoublingRooms/{roomId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && (
        // Room creator can modify
        resource.data.createdBy == request.auth.uid ||
        // Users can join/leave (update participants array)
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['currentParticipants'])
      );
      allow create: if request.auth != null && 
        request.resource.data.createdBy == request.auth.uid;
    }
  }
}
