# FocusFlow API Documentation

## Base URL
- **Production**: `https://us-central1-focusflow-prod.cloudfunctions.net/api`
- **Staging**: `https://us-central1-focusflow-staging.cloudfunctions.net/api`

## Authentication
All API requests require Firebase Authentication token in the Authorization header:
\`\`\`
Authorization: Bearer <firebase_id_token>
\`\`\`

## Rate Limits
- **General API**: 100 requests per 15 minutes
- **AI Endpoints**: 10 requests per minute
- **Auth Endpoints**: 5 requests per 15 minutes

## Endpoints

### Tasks

#### GET /tasks
Get user's tasks for a specific date.

**Query Parameters:**
- `date` (string, required): Date in YYYY-MM-DD format
- `limit` (number, optional): Max tasks to return (default: 50)

**Response:**
\`\`\`json
{
  "tasks": [
    {
      "id": "task_123",
      "title": "Complete project proposal",
      "description": "Write and review the Q1 project proposal",
      "status": "pending",
      "priority": "high",
      "energyLevel": "high",
      "estimatedMinutes": 60,
      "actualMinutes": null,
      "createdAt": "2024-01-15T10:00:00Z",
      "completedAt": null,
      "aiGenerated": false,
      "subtasks": [
        {
          "id": "subtask_456",
          "title": "Research market trends",
          "completed": false,
          "estimatedMinutes": 20
        }
      ]
    }
  ],
  "totalCount": 5
}
\`\`\`

#### POST /tasks
Create a new task.

**Request Body:**
\`\`\`json
{
  "title": "Complete project proposal",
  "description": "Write and review the Q1 project proposal",
  "priority": "high",
  "energyLevel": "high",
  "estimatedMinutes": 60,
  "dueDate": "2024-01-20T17:00:00Z"
}
\`\`\`

**Response:**
\`\`\`json
{
  "task": {
    "id": "task_123",
    "title": "Complete project proposal",
    "status": "pending",
    "createdAt": "2024-01-15T10:00:00Z"
  }
}
\`\`\`

#### PUT /tasks/:id
Update an existing task.

#### DELETE /tasks/:id
Delete a task.

#### POST /tasks/:id/complete
Mark a task as completed.

**Request Body:**
\`\`\`json
{
  "actualMinutes": 45,
  "completionNotes": "Finished earlier than expected"
}
\`\`\`

### AI Services

#### POST /ai/breakdown-task
Break down a complex task into smaller subtasks.

**Request Body:**
\`\`\`json
{
  "taskTitle": "Plan birthday party",
  "taskDescription": "Organize a surprise birthday party for my friend",
  "userContext": {
    "energyLevel": "medium",
    "availableTime": 120,
    "preferences": ["creative", "social"]
  }
}
\`\`\`

**Response:**
\`\`\`json
{
  "breakdown": {
    "subtasks": [
      {
        "title": "Choose party theme and decorations",
        "estimatedMinutes": 20,
        "energyLevel": "medium",
        "tips": "Browse Pinterest for 10 minutes, then decide"
      },
      {
        "title": "Create guest list and send invitations",
        "estimatedMinutes": 30,
        "energyLevel": "low",
        "tips": "Use a simple group text or Facebook event"
      }
    ],
    "totalEstimatedMinutes": 120,
    "adhdTips": [
      "Set a timer for each subtask to stay focused",
      "Take a 5-minute break between planning tasks"
    ]
  },
  "cached": false
}
\`\`\`

#### POST /ai/weekly-reflection
Generate AI insights for weekly reflection.

**Request Body:**
\`\`\`json
{
  "weekData": {
    "completedTasks": 15,
    "totalTasks": 20,
    "averageEnergyLevel": 3.2,
    "focusSessionsCompleted": 8,
    "mostProductiveDay": "Tuesday",
    "challengingAreas": ["time estimation", "task switching"]
  }
}
\`\`\`

**Response:**
\`\`\`json
{
  "reflection": {
    "insights": [
      "You completed 75% of your tasks this week - that's excellent!",
      "Tuesday seems to be your most productive day. Consider scheduling important tasks then."
    ],
    "recommendations": [
      "Try adding 25% buffer time to your estimates",
      "Use the Pomodoro technique for better task switching"
    ],
    "celebrationMessage": "You're building great habits! Keep up the momentum.",
    "nextWeekFocus": "Focus on improving time estimation accuracy"
  }
}
\`\`\`

### User Profile

#### GET /user/profile
Get user profile and preferences.

#### PUT /user/profile
Update user profile.

#### GET /user/analytics
Get user analytics and insights.

**Response:**
\`\`\`json
{
  "analytics": {
    "weeklyStats": {
      "tasksCompleted": 15,
      "focusMinutes": 480,
      "averageEnergyLevel": 3.2,
      "completionRate": 0.75
    },
    "monthlyTrends": {
      "completionRateChange": 0.15,
      "focusTimeChange": 120,
      "mostProductiveHour": 10
    },
    "insights": [
      "Your completion rate improved by 15% this month!",
      "You focus best around 10 AM"
    ]
  }
}
\`\`\`

### Focus Sessions

#### POST /focus/start
Start a focus session.

#### PUT /focus/:id/complete
Complete a focus session.

#### GET /focus/history
Get focus session history.

## Error Responses

All errors follow this format:
\`\`\`json
{
  "error": "Invalid input",
  "code": "VALIDATION_ERROR",
  "details": [
    "Title is required",
    "Priority must be one of: low, medium, high"
  ],
  "timestamp": "2024-01-15T10:00:00Z"
}
\`\`\`

### Error Codes
- `VALIDATION_ERROR`: Invalid request data
- `UNAUTHORIZED`: Invalid or missing auth token
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `RATE_LIMITED`: Too many requests
- `AI_SERVICE_ERROR`: AI service unavailable
- `INTERNAL_ERROR`: Server error

## Webhooks

### Task Completion
Triggered when a user completes a task.

**Payload:**
\`\`\`json
{
  "event": "task.completed",
  "userId": "user_123",
  "taskId": "task_456",
  "completedAt": "2024-01-15T10:00:00Z",
  "actualMinutes": 45
}
\`\`\`

### Weekly Reflection
Triggered when a user completes their weekly reflection.

## SDK Examples

### JavaScript/TypeScript
\`\`\`typescript
import { FocusFlowAPI } from '@focusflow/api-client'

const api = new FocusFlowAPI({
  baseURL: 'https://us-central1-focusflow-prod.cloudfunctions.net/api',
  authToken: firebaseIdToken
})

// Get today's tasks
const tasks = await api.tasks.list({ date: '2024-01-15' })

// Break down a task
const breakdown = await api.ai.breakdownTask({
  taskTitle: 'Plan birthday party',
  taskDescription: 'Organize a surprise party',
  userContext: { energyLevel: 'medium' }
})
\`\`\`

### Dart/Flutter
\`\`\`dart
import 'package:focusflow_api/focusflow_api.dart';

final api = FocusFlowAPI(
  baseUrl: 'https://us-central1-focusflow-prod.cloudfunctions.net/api',
  authToken: firebaseIdToken,
);

// Get today's tasks
final tasks = await api.tasks.list(date: '2024-01-15');

// Break down a task
final breakdown = await api.ai.breakdownTask(
  taskTitle: 'Plan birthday party',
  taskDescription: 'Organize a surprise party',
  userContext: UserContext(energyLevel: EnergyLevel.medium),
);
