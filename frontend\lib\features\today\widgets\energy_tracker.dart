import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class EnergyTracker extends StatefulWidget {
  final int currentLevel;
  final Function(int) onLevelChanged;

  const EnergyTracker({
    super.key,
    required this.currentLevel,
    required this.onLevelChanged,
  });

  @override
  State<EnergyTracker> createState() => _EnergyTrackerState();
}

class _EnergyTrackerState extends State<EnergyTracker>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(5, (index) {
        final level = index + 1;
        final isSelected = level <= widget.currentLevel;
        final isCurrentLevel = level == widget.currentLevel;
        
        return GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            widget.onLevelChanged(level);
            if (isCurrentLevel) {
              _animationController.forward().then((_) {
                _animationController.reverse();
              });
            }
          },
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: isCurrentLevel ? _scaleAnimation.value : 1.0,
                child: Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? _getEnergyColor(level)
                        : theme.colorScheme.outline.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: isSelected 
                          ? _getEnergyColor(level)
                          : theme.colorScheme.outline.withOpacity(0.4),
                      width: 2,
                    ),
                    boxShadow: isSelected ? [
                      BoxShadow(
                        color: _getEnergyColor(level).withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ] : null,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _getEnergyIcon(level),
                        size: 20,
                        color: isSelected 
                            ? Colors.white
                            : theme.colorScheme.onSurface.withOpacity(0.5),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        level.toString(),
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: isSelected 
                              ? Colors.white
                              : theme.colorScheme.onSurface.withOpacity(0.5),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      }),
    );
  }

  Color _getEnergyColor(int level) {
    switch (level) {
      case 1:
        return const Color(0xFF6C757D); // Gray
      case 2:
        return const Color(0xFFFFC107); // Yellow
      case 3:
        return const Color(0xFF17A2B8); // Cyan
      case 4:
        return const Color(0xFF28A745); // Green
      case 5:
        return const Color(0xFF007BFF); // Blue
      default:
        return const Color(0xFF6C757D);
    }
  }

  IconData _getEnergyIcon(int level) {
    switch (level) {
      case 1:
        return Icons.battery_1_bar;
      case 2:
        return Icons.battery_2_bar;
      case 3:
        return Icons.battery_4_bar;
      case 4:
        return Icons.battery_5_bar;
      case 5:
        return Icons.battery_full;
      default:
        return Icons.battery_unknown;
    }
  }
}
