import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// ADHD-friendly text widget with accessibility features
class AccessibilityText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final bool isDyslexiaFriendly;
  final bool isHighContrast;
  final double? letterSpacing;
  final double? wordSpacing;
  final double? lineHeight;
  final String? semanticsLabel;

  const AccessibilityText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.isDyslexiaFriendly = false,
    this.isHighContrast = false,
    this.letterSpacing,
    this.wordSpacing,
    this.lineHeight,
    this.semanticsLabel,
  });

  /// Factory constructor for dyslexia-friendly text
  factory AccessibilityText.dyslexiaFriendly(
    String text, {
    Key? key,
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    String? semanticsLabel,
  }) {
    return AccessibilityText(
      text,
      key: key,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      isDyslexiaFriendly: true,
      letterSpacing: 1.2,
      wordSpacing: 2.0,
      lineHeight: 1.8,
      semanticsLabel: semanticsLabel,
    );
  }

  /// Factory constructor for high contrast text
  factory AccessibilityText.highContrast(
    String text, {
    Key? key,
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    String? semanticsLabel,
  }) {
    return AccessibilityText(
      text,
      key: key,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      isHighContrast: true,
      semanticsLabel: semanticsLabel,
    );
  }

  /// Factory constructor for ADHD-optimized headings
  factory AccessibilityText.heading(
    String text, {
    Key? key,
    int level = 1,
    Color? color,
    TextAlign? textAlign,
    String? semanticsLabel,
  }) {
    return AccessibilityText(
      text,
      key: key,
      style: _getHeadingStyle(level, color),
      textAlign: textAlign,
      letterSpacing: 0.5,
      lineHeight: 1.3,
      semanticsLabel: semanticsLabel,
    );
  }

  /// Factory constructor for body text with ADHD optimizations
  factory AccessibilityText.body(
    String text, {
    Key? key,
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    String? semanticsLabel,
  }) {
    return AccessibilityText(
      text,
      key: key,
      style: _getBodyStyle(color),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      letterSpacing: 0.2,
      lineHeight: 1.6,
      semanticsLabel: semanticsLabel,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Build the text style with accessibility enhancements
    TextStyle effectiveStyle = _buildAccessibleStyle(context, theme);
    
    return Semantics(
      label: semanticsLabel ?? text,
      child: Text(
        text,
        style: effectiveStyle,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
        textScaleFactor: 1.0, // Prevent system scaling issues
      ),
    );
  }

  TextStyle _buildAccessibleStyle(BuildContext context, ThemeData theme) {
    TextStyle baseStyle = style ?? theme.textTheme.bodyMedium ?? const TextStyle();
    
    // Apply dyslexia-friendly font if needed
    if (isDyslexiaFriendly) {
      baseStyle = GoogleFonts.openDyslexic(
        textStyle: baseStyle,
        letterSpacing: letterSpacing ?? 1.2,
        wordSpacing: wordSpacing ?? 2.0,
        height: lineHeight ?? 1.8,
      );
    } else {
      // Use Inter font with ADHD-friendly spacing
      baseStyle = GoogleFonts.inter(
        textStyle: baseStyle,
        letterSpacing: letterSpacing ?? baseStyle.letterSpacing ?? 0.1,
        wordSpacing: wordSpacing ?? 1.0,
        height: lineHeight ?? baseStyle.height ?? 1.5,
      );
    }
    
    // Apply high contrast if needed
    if (isHighContrast) {
      final brightness = theme.brightness;
      final highContrastColor = brightness == Brightness.dark
          ? Colors.white
          : Colors.black;
      
      baseStyle = baseStyle.copyWith(
        color: highContrastColor,
        fontWeight: FontWeight.w600,
        shadows: [
          Shadow(
            color: brightness == Brightness.dark ? Colors.black : Colors.white,
            offset: const Offset(0.5, 0.5),
            blurRadius: 1.0,
          ),
        ],
      );
    }
    
    return baseStyle;
  }

  static TextStyle _getHeadingStyle(int level, Color? color) {
    switch (level) {
      case 1:
        return TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w700,
          color: color,
        );
      case 2:
        return TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: color,
        );
      case 3:
        return TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: color,
        );
      case 4:
        return TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: color,
        );
      default:
        return TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: color,
        );
    }
  }

  static TextStyle _getBodyStyle(Color? color) {
    return TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: color,
    );
  }
}

/// ADHD-friendly rich text widget for complex text formatting
class AccessibilityRichText extends StatelessWidget {
  final List<TextSpan> children;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final bool isDyslexiaFriendly;
  final String? semanticsLabel;

  const AccessibilityRichText({
    super.key,
    required this.children,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.isDyslexiaFriendly = false,
    this.semanticsLabel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Apply accessibility enhancements to all text spans
    final enhancedChildren = children.map((span) {
      if (span.style != null) {
        TextStyle enhancedStyle = span.style!;
        
        if (isDyslexiaFriendly) {
          enhancedStyle = GoogleFonts.openDyslexic(
            textStyle: enhancedStyle,
            letterSpacing: 1.2,
            wordSpacing: 2.0,
            height: 1.8,
          );
        } else {
          enhancedStyle = GoogleFonts.inter(
            textStyle: enhancedStyle,
            letterSpacing: enhancedStyle.letterSpacing ?? 0.1,
            height: enhancedStyle.height ?? 1.5,
          );
        }
        
        return span.copyWith(style: enhancedStyle);
      }
      return span;
    }).toList();
    
    return Semantics(
      label: semanticsLabel,
      child: RichText(
        text: TextSpan(children: enhancedChildren),
        textAlign: textAlign ?? TextAlign.start,
        maxLines: maxLines,
        overflow: overflow ?? TextOverflow.clip,
        textScaleFactor: 1.0,
      ),
    );
  }
}

/// Helper class for ADHD-friendly text utilities
class ADHDTextUtils {
  /// Breaks long text into smaller, digestible chunks
  static List<String> breakIntoChunks(String text, {int maxWordsPerChunk = 15}) {
    final words = text.split(' ');
    final chunks = <String>[];
    
    for (int i = 0; i < words.length; i += maxWordsPerChunk) {
      final end = (i + maxWordsPerChunk < words.length) 
          ? i + maxWordsPerChunk 
          : words.length;
      chunks.add(words.sublist(i, end).join(' '));
    }
    
    return chunks;
  }
  
  /// Adds visual breaks to long text for better readability
  static String addVisualBreaks(String text) {
    return text
        .replaceAll('. ', '.\n\n')
        .replaceAll('! ', '!\n\n')
        .replaceAll('? ', '?\n\n');
  }
  
  /// Highlights keywords in text for better focus
  static List<TextSpan> highlightKeywords(
    String text, 
    List<String> keywords, {
    Color? highlightColor,
    TextStyle? baseStyle,
    TextStyle? highlightStyle,
  }) {
    final spans = <TextSpan>[];
    String remainingText = text;
    
    for (final keyword in keywords) {
      final index = remainingText.toLowerCase().indexOf(keyword.toLowerCase());
      if (index != -1) {
        // Add text before keyword
        if (index > 0) {
          spans.add(TextSpan(
            text: remainingText.substring(0, index),
            style: baseStyle,
          ));
        }
        
        // Add highlighted keyword
        spans.add(TextSpan(
          text: remainingText.substring(index, index + keyword.length),
          style: highlightStyle ?? TextStyle(
            backgroundColor: highlightColor ?? Colors.yellow.withOpacity(0.3),
            fontWeight: FontWeight.w600,
          ),
        ));
        
        remainingText = remainingText.substring(index + keyword.length);
      }
    }
    
    // Add remaining text
    if (remainingText.isNotEmpty) {
      spans.add(TextSpan(
        text: remainingText,
        style: baseStyle,
      ));
    }
    
    return spans;
  }
}
