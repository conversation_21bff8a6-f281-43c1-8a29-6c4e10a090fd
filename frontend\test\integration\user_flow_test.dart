import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';

import 'package:focusflow/main.dart' as app;
import 'package:focusflow/shared/providers/auth_provider.dart';
import 'package:focusflow/shared/providers/theme_provider.dart';
import 'package:focusflow/shared/providers/subscription_provider.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('FocusFlow User Flow Integration Tests', () {
    testWidgets('Complete onboarding flow', (tester) async {
      // Launch app
      app.main();
      await tester.pumpAndSettle();

      // Should show splash screen first
      expect(find.text('FocusFlow'), findsOneWidget);
      
      // Wait for splash to complete
      await tester.pumpAndSettle(const Duration(seconds: 4));

      // Should navigate to login screen for new users
      expect(find.text('Welcome Back'), findsOneWidget);
      
      // Navigate to signup
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Fill signup form
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 'testpassword123');
      await tester.enterText(find.byKey(const Key('name_field')), 'Test User');
      
      // Submit signup
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Should navigate to onboarding
      expect(find.text('Welcome to FocusFlow'), findsOneWidget);
      
      // Complete onboarding steps
      await _completeOnboarding(tester);

      // Should reach main app
      expect(find.text('Today\'s Three'), findsOneWidget);
    });

    testWidgets('Create and complete task flow', (tester) async {
      // Assume user is logged in and on main screen
      await _loginTestUser(tester);
      
      // Navigate to add task
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Fill task form
      await tester.enterText(
        find.byKey(const Key('task_title_field')), 
        'Write integration tests'
      );
      await tester.enterText(
        find.byKey(const Key('task_description_field')), 
        'Create comprehensive integration tests for the app'
      );
      
      // Set priority
      await tester.tap(find.text('High Priority'));
      await tester.pumpAndSettle();

      // Request AI breakdown
      await tester.tap(find.text('Get AI Breakdown'));
      await tester.pumpAndSettle();

      // Wait for AI response
      await tester.pump(const Duration(seconds: 3));
      
      // Should show breakdown steps
      expect(find.text('AI Breakdown'), findsOneWidget);
      
      // Save task
      await tester.tap(find.text('Save Task'));
      await tester.pumpAndSettle();

      // Should return to main screen with new task
      expect(find.text('Write integration tests'), findsOneWidget);
      
      // Complete the task
      await tester.tap(find.byIcon(Icons.check_circle_outline));
      await tester.pumpAndSettle();

      // Should show completion celebration
      expect(find.text('Amazing work!'), findsOneWidget);
      
      // Dismiss celebration
      await tester.tap(find.text('Continue'));
      await tester.pumpAndSettle();

      // Task should be marked as completed
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('Focus mode session flow', (tester) async {
      await _loginTestUser(tester);
      
      // Navigate to focus mode
      await tester.tap(find.text('Focus'));
      await tester.pumpAndSettle();

      // Should show focus mode screen
      expect(find.text('Focus Mode'), findsOneWidget);
      
      // Set timer duration
      await tester.tap(find.text('25 min'));
      await tester.pumpAndSettle();

      // Start focus session
      await tester.tap(find.text('Start Focus'));
      await tester.pumpAndSettle();

      // Should show running timer
      expect(find.text('25:00'), findsOneWidget);
      expect(find.text('Pause'), findsOneWidget);
      
      // Let timer run for a few seconds
      await tester.pump(const Duration(seconds: 3));
      
      // Timer should have decreased
      expect(find.text('24:57'), findsOneWidget);
      
      // Pause session
      await tester.tap(find.text('Pause'));
      await tester.pumpAndSettle();

      // Should show paused state
      expect(find.text('Start Focus'), findsOneWidget);
      
      // Resume session
      await tester.tap(find.text('Start Focus'));
      await tester.pumpAndSettle();

      // Stop session
      await tester.tap(find.text('Stop'));
      await tester.pumpAndSettle();

      // Confirm stop
      await tester.tap(find.text('Stop'));
      await tester.pumpAndSettle();

      // Should return to initial state
      expect(find.text('Start Focus'), findsOneWidget);
    });

    testWidgets('Body doubling room flow', (tester) async {
      await _loginProUser(tester);
      
      // Navigate to body doubling
      await tester.tap(find.text('Focus'));
      await tester.pumpAndSettle();
      
      await tester.tap(find.text('Body Doubling'));
      await tester.pumpAndSettle();

      // Should show body doubling screen
      expect(find.text('Body Doubling'), findsOneWidget);
      
      // Quick join room
      await tester.tap(find.text('Quick Join'));
      await tester.pumpAndSettle();

      // Should either join existing room or create new one
      await tester.pump(const Duration(seconds: 2));
      
      // Should show active session
      expect(find.text('people focusing'), findsOneWidget);
      
      // Leave room
      await tester.tap(find.byIcon(Icons.exit_to_app));
      await tester.pumpAndSettle();

      // Should return to room selection
      expect(find.text('Quick Join'), findsOneWidget);
    });

    testWidgets('Weekly reflection flow', (tester) async {
      await _loginProUser(tester);
      
      // Navigate to reflection
      await tester.tap(find.text('Reflection'));
      await tester.pumpAndSettle();

      // Should show reflection screen
      expect(find.text('Weekly Reflection'), findsOneWidget);
      
      // Generate reflection
      await tester.tap(find.text('Generate Reflection'));
      await tester.pumpAndSettle();

      // Wait for AI to generate reflection
      await tester.pump(const Duration(seconds: 5));
      
      // Should show reflection content
      expect(find.text('Week of'), findsOneWidget);
      expect(find.text('Key Insights'), findsOneWidget);
      expect(find.text('Next Week\'s Focus'), findsOneWidget);
    });

    testWidgets('Subscription upgrade flow', (tester) async {
      await _loginTestUser(tester);
      
      // Try to access pro feature
      await tester.tap(find.text('Focus'));
      await tester.pumpAndSettle();
      
      await tester.tap(find.text('Body Doubling'));
      await tester.pumpAndSettle();

      // Should show upgrade prompt
      expect(find.text('Upgrade to Pro'), findsOneWidget);
      
      // Tap upgrade
      await tester.tap(find.text('Upgrade to Pro'));
      await tester.pumpAndSettle();

      // Should show paywall
      expect(find.text('Unlock Your ADHD Superpowers'), findsOneWidget);
      expect(find.text('Start Free Trial'), findsOneWidget);
      
      // Close paywall
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      // Should return to previous screen
      expect(find.text('Body Doubling'), findsOneWidget);
    });

    testWidgets('Settings and privacy flow', (tester) async {
      await _loginTestUser(tester);
      
      // Navigate to profile
      await tester.tap(find.text('Profile'));
      await tester.pumpAndSettle();

      // Navigate to settings
      await tester.tap(find.text('Settings'));
      await tester.pumpAndSettle();

      // Should show settings screen
      expect(find.text('Settings'), findsOneWidget);
      
      // Navigate to privacy settings
      await tester.tap(find.text('Privacy & Security'));
      await tester.pumpAndSettle();

      // Should show privacy screen
      expect(find.text('Privacy & Security'), findsOneWidget);
      
      // Toggle a setting
      await tester.tap(find.byType(Switch).first);
      await tester.pumpAndSettle();

      // Export data
      await tester.tap(find.text('Export My Data'));
      await tester.pumpAndSettle();

      // Should show export progress or completion
      expect(find.text('Export'), findsOneWidget);
    });

    testWidgets('Theme switching flow', (tester) async {
      await _loginTestUser(tester);
      
      // Navigate to settings
      await tester.tap(find.text('Profile'));
      await tester.pumpAndSettle();
      
      await tester.tap(find.text('Settings'));
      await tester.pumpAndSettle();

      // Find theme setting
      await tester.tap(find.text('Theme'));
      await tester.pumpAndSettle();

      // Switch to dark theme
      await tester.tap(find.text('Dark'));
      await tester.pumpAndSettle();

      // Should apply dark theme
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold).first);
      expect(scaffold.backgroundColor, isNot(Colors.white));
      
      // Switch to pastel theme
      await tester.tap(find.text('Pastel'));
      await tester.pumpAndSettle();

      // Should apply pastel theme
      expect(find.text('Pastel'), findsOneWidget);
    });
  });

  // Helper methods
  Future<void> _completeOnboarding(WidgetTester tester) async {
    // Step 1: ADHD challenges
    await tester.tap(find.text('Difficulty focusing'));
    await tester.tap(find.text('Task overwhelm'));
    await tester.tap(find.text('Next'));
    await tester.pumpAndSettle();

    // Step 2: Work preferences
    await tester.tap(find.text('Pomodoro'));
    await tester.tap(find.text('Next'));
    await tester.pumpAndSettle();

    // Step 3: Notification preferences
    await tester.tap(find.text('Enable Notifications'));
    await tester.tap(find.text('Next'));
    await tester.pumpAndSettle();

    // Step 4: Complete
    await tester.tap(find.text('Get Started'));
    await tester.pumpAndSettle();
  }

  Future<void> _loginTestUser(WidgetTester tester) async {
    // Mock login for testing
    // In real tests, this would use test credentials
    app.main();
    await tester.pumpAndSettle();
    
    // Skip splash
    await tester.pumpAndSettle(const Duration(seconds: 4));
    
    // Assume already logged in for integration tests
    // or implement actual login flow
  }

  Future<void> _loginProUser(WidgetTester tester) async {
    await _loginTestUser(tester);
    // Mock pro subscription for testing
  }
}
