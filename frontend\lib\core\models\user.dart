import 'package:cloud_firestore/cloud_firestore.dart';

class User {
  final String id;
  final String email;
  final String displayName;
  final DateTime createdAt;
  final Subscription subscription;
  final UserPreferences preferences;
  final UserStats stats;
  final OnboardingData? onboarding;

  User({
    required this.id,
    required this.email,
    required this.displayName,
    required this.createdAt,
    required this.subscription,
    required this.preferences,
    required this.stats,
    this.onboarding,
  });

  factory User.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return User(
      id: doc.id,
      email: data['email'] ?? '',
      displayName: data['displayName'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      subscription: Subscription.fromMap(data['subscription'] ?? {}),
      preferences: UserPreferences.fromMap(data['preferences'] ?? {}),
      stats: UserStats.fromMap(data['stats'] ?? {}),
      onboarding: data['onboarding'] != null 
          ? OnboardingData.fromMap(data['onboarding'])
          : null,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'displayName': displayName,
      'createdAt': Timestamp.fromDate(createdAt),
      'subscription': subscription.toMap(),
      'preferences': preferences.toMap(),
      'stats': stats.toMap(),
      'onboarding': onboarding?.toMap(),
    };
  }

  User copyWith({
    String? email,
    String? displayName,
    Subscription? subscription,
    UserPreferences? preferences,
    UserStats? stats,
    OnboardingData? onboarding,
  }) {
    return User(
      id: id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      createdAt: createdAt,
      subscription: subscription ?? this.subscription,
      preferences: preferences ?? this.preferences,
      stats: stats ?? this.stats,
      onboarding: onboarding ?? this.onboarding,
    );
  }
}

class Subscription {
  final SubscriptionTier tier;
  final DateTime? expiresAt;
  final String provider;
  final String? originalTransactionId;

  Subscription({
    required this.tier,
    this.expiresAt,
    required this.provider,
    this.originalTransactionId,
  });

  factory Subscription.fromMap(Map<String, dynamic> map) {
    return Subscription(
      tier: SubscriptionTier.values.firstWhere(
        (e) => e.name == map['tier'],
        orElse: () => SubscriptionTier.free,
      ),
      expiresAt: map['expiresAt'] != null 
          ? (map['expiresAt'] as Timestamp).toDate()
          : null,
      provider: map['provider'] ?? 'revenueCat',
      originalTransactionId: map['originalTransactionId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'tier': tier.name,
      'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
      'provider': provider,
      'originalTransactionId': originalTransactionId,
    };
  }

  bool get isPro => tier == SubscriptionTier.pro && 
      (expiresAt == null || expiresAt!.isAfter(DateTime.now()));
}

enum SubscriptionTier { free, pro }

class UserPreferences {
  final AppThemeType theme;
  final FocusModeType focusMode;
  final bool notifications;
  final bool energyTracking;
  final bool soundEnabled;
  final int defaultFocusMinutes;

  UserPreferences({
    required this.theme,
    required this.focusMode,
    required this.notifications,
    required this.energyTracking,
    required this.soundEnabled,
    required this.defaultFocusMinutes,
  });

  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      theme: AppThemeType.values.firstWhere(
        (e) => e.name == map['theme'],
        orElse: () => AppThemeType.light,
      ),
      focusMode: FocusModeType.values.firstWhere(
        (e) => e.name == map['focusMode'],
        orElse: () => FocusModeType.pomodoro,
      ),
      notifications: map['notifications'] ?? true,
      energyTracking: map['energyTracking'] ?? true,
      soundEnabled: map['soundEnabled'] ?? true,
      defaultFocusMinutes: map['defaultFocusMinutes'] ?? 25,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'theme': theme.name,
      'focusMode': focusMode.name,
      'notifications': notifications,
      'energyTracking': energyTracking,
      'soundEnabled': soundEnabled,
      'defaultFocusMinutes': defaultFocusMinutes,
    };
  }

  UserPreferences copyWith({
    AppThemeType? theme,
    FocusModeType? focusMode,
    bool? notifications,
    bool? energyTracking,
    bool? soundEnabled,
    int? defaultFocusMinutes,
  }) {
    return UserPreferences(
      theme: theme ?? this.theme,
      focusMode: focusMode ?? this.focusMode,
      notifications: notifications ?? this.notifications,
      energyTracking: energyTracking ?? this.energyTracking,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      defaultFocusMinutes: defaultFocusMinutes ?? this.defaultFocusMinutes,
    );
  }
}

enum AppThemeType { light, dark, pastel, dyslexia }
enum FocusModeType { pomodoro, custom, deepWork }

class UserStats {
  final int totalTasksCompleted;
  final int currentStreak;
  final int longestStreak;
  final int totalFocusMinutes;
  final int weeklyGoal;
  final String lastActiveDate;

  UserStats({
    required this.totalTasksCompleted,
    required this.currentStreak,
    required this.longestStreak,
    required this.totalFocusMinutes,
    required this.weeklyGoal,
    required this.lastActiveDate,
  });

  factory UserStats.fromMap(Map<String, dynamic> map) {
    return UserStats(
      totalTasksCompleted: map['totalTasksCompleted'] ?? 0,
      currentStreak: map['currentStreak'] ?? 0,
      longestStreak: map['longestStreak'] ?? 0,
      totalFocusMinutes: map['totalFocusMinutes'] ?? 0,
      weeklyGoal: map['weeklyGoal'] ?? 3,
      lastActiveDate: map['lastActiveDate'] ?? DateTime.now().toIso8601String().split('T')[0],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'totalTasksCompleted': totalTasksCompleted,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'totalFocusMinutes': totalFocusMinutes,
      'weeklyGoal': weeklyGoal,
      'lastActiveDate': lastActiveDate,
    };
  }

  UserStats copyWith({
    int? totalTasksCompleted,
    int? currentStreak,
    int? longestStreak,
    int? totalFocusMinutes,
    int? weeklyGoal,
    String? lastActiveDate,
  }) {
    return UserStats(
      totalTasksCompleted: totalTasksCompleted ?? this.totalTasksCompleted,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      totalFocusMinutes: totalFocusMinutes ?? this.totalFocusMinutes,
      weeklyGoal: weeklyGoal ?? this.weeklyGoal,
      lastActiveDate: lastActiveDate ?? this.lastActiveDate,
    );
  }
}

class OnboardingData {
  final bool completed;
  final int adhdAssessmentScore;
  final List<String> primaryChallenges;
  final List<String> goals;

  OnboardingData({
    required this.completed,
    required this.adhdAssessmentScore,
    required this.primaryChallenges,
    required this.goals,
  });

  factory OnboardingData.fromMap(Map<String, dynamic> map) {
    return OnboardingData(
      completed: map['completed'] ?? false,
      adhdAssessmentScore: map['adhdAssessmentScore'] ?? 0,
      primaryChallenges: List<String>.from(map['primaryChallenges'] ?? []),
      goals: List<String>.from(map['goals'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'completed': completed,
      'adhdAssessmentScore': adhdAssessmentScore,
      'primaryChallenges': primaryChallenges,
      'goals': goals,
    };
  }
}
