import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';

import '../../../shared/widgets/calm_card.dart';
import '../../../shared/widgets/adhd_button.dart';
import '../providers/focus_provider.dart';

class FocusTimerWidget extends StatefulWidget {
  const FocusTimerWidget({super.key});

  @override
  State<FocusTimerWidget> createState() => _FocusTimerWidgetState();
}

class _FocusTimerWidgetState extends State<FocusTimerWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Consumer<FocusProvider>(
      builder: (context, focusProvider, child) {
        // Start/stop pulse animation based on timer state
        if (focusProvider.isRunning && !_pulseController.isAnimating) {
          _pulseController.repeat(reverse: true);
        } else if (!focusProvider.isRunning && _pulseController.isAnimating) {
          _pulseController.stop();
          _pulseController.reset();
        }

        return CalmCard(
          backgroundColor: theme.colorScheme.primaryContainer.withOpacity(0.1),
          child: Column(
            children: [
              // Timer display
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: focusProvider.isRunning ? _pulseAnimation.value : 1.0,
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        border: Border.all(
                          color: theme.colorScheme.primary,
                          width: 3,
                        ),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              _formatTime(focusProvider.remainingSeconds),
                              style: theme.textTheme.displayMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                                fontFeatures: [const FontFeature.tabularFigures()],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              focusProvider.currentPhase,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurface.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 24),
              
              // Progress indicator
              LinearProgressIndicator(
                value: focusProvider.progress,
                backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                minHeight: 6,
              ),
              const SizedBox(height: 24),
              
              // Timer controls
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  if (!focusProvider.isRunning) ...[
                    Expanded(
                      child: ADHDButton(
                        text: 'Start Focus',
                        icon: Icons.play_arrow,
                        type: ADHDButtonType.primary,
                        onPressed: () => focusProvider.startTimer(),
                      ),
                    ),
                  ] else ...[
                    Expanded(
                      child: ADHDButton(
                        text: 'Pause',
                        icon: Icons.pause,
                        type: ADHDButtonType.secondary,
                        onPressed: () => focusProvider.pauseTimer(),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ADHDButton(
                        text: 'Stop',
                        icon: Icons.stop,
                        type: ADHDButtonType.outline,
                        onPressed: () => _showStopConfirmation(context, focusProvider),
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 16),
              
              // Timer settings
              if (!focusProvider.isRunning) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildTimerOption(context, '15 min', 15, focusProvider),
                    _buildTimerOption(context, '25 min', 25, focusProvider),
                    _buildTimerOption(context, '45 min', 45, focusProvider),
                    _buildTimerOption(context, 'Custom', 0, focusProvider),
                  ],
                ),
              ],
              
              // Session stats
              if (focusProvider.completedSessions > 0) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.secondaryContainer.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem(
                        context,
                        'Sessions',
                        focusProvider.completedSessions.toString(),
                        Icons.psychology,
                      ),
                      _buildStatItem(
                        context,
                        'Total Time',
                        '${focusProvider.totalFocusMinutes}m',
                        Icons.timer,
                      ),
                      _buildStatItem(
                        context,
                        'Streak',
                        '${focusProvider.currentStreak}',
                        Icons.local_fire_department,
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildTimerOption(
    BuildContext context,
    String label,
    int minutes,
    FocusProvider focusProvider,
  ) {
    final theme = Theme.of(context);
    final isSelected = focusProvider.selectedMinutes == minutes;
    
    return GestureDetector(
      onTap: () {
        if (minutes == 0) {
          _showCustomTimerDialog(context, focusProvider);
        } else {
          focusProvider.setTimerDuration(minutes);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected 
              ? theme.colorScheme.primary
              : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected 
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withOpacity(0.3),
          ),
        ),
        child: Text(
          label,
          style: theme.textTheme.labelMedium?.copyWith(
            color: isSelected 
                ? theme.colorScheme.onPrimary
                : theme.colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 20,
          color: theme.colorScheme.secondary,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.secondary,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.labelSmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void _showCustomTimerDialog(BuildContext context, FocusProvider focusProvider) {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Custom Timer'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Minutes',
            hintText: 'Enter duration in minutes',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final minutes = int.tryParse(controller.text);
              if (minutes != null && minutes > 0 && minutes <= 180) {
                focusProvider.setTimerDuration(minutes);
                Navigator.pop(context);
              }
            },
            child: const Text('Set'),
          ),
        ],
      ),
    );
  }

  void _showStopConfirmation(BuildContext context, FocusProvider focusProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Stop Focus Session?'),
        content: const Text(
          'Are you sure you want to stop your focus session? Your progress will be saved.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Continue'),
          ),
          ElevatedButton(
            onPressed: () {
              focusProvider.stopTimer();
              Navigator.pop(context);
            },
            child: const Text('Stop'),
          ),
        ],
      ),
    );
  }
}
