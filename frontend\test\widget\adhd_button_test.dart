import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:focusflow/shared/widgets/adhd_button.dart';

void main() {
  group('ADHDButton Widget Tests', () {
    testWidgets('should render primary button correctly', (tester) async {
      // Arrange
      bool wasPressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ADHDButton(
              text: 'Test Button',
              icon: Icons.star,
              type: ADHDButtonType.primary,
              onPressed: () => wasPressed = true,
            ),
          ),
        ),
      );

      // Act & Assert
      expect(find.text('Test Button'), findsOneWidget);
      expect(find.byIcon(Icons.star), findsOneWidget);
      
      await tester.tap(find.byType(ADHDButton));
      expect(wasPressed, isTrue);
    });

    testWidgets('should show loading state correctly', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AD<PERSON><PERSON>utton(
              text: 'Loading Button',
              type: ADHDButtonType.primary,
              isLoading: true,
              onPressed: () {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading Button'), findsNothing);
    });

    testWidgets('should be disabled when onPressed is null', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ADHDButton(
              text: 'Disabled Button',
              type: ADHDButtonType.primary,
              onPressed: null,
            ),
          ),
        ),
      );

      // Act
      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      
      // Assert
      expect(button.onPressed, isNull);
    });

    testWidgets('should apply correct styling for different types', (tester) async {
      // Test primary button
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                ADHDButton(
                  text: 'Primary',
                  type: ADHDButtonType.primary,
                  onPressed: () {},
                ),
                ADHDButton(
                  text: 'Secondary',
                  type: ADHDButtonType.secondary,
                  onPressed: () {},
                ),
                ADHDButton(
                  text: 'Outline',
                  type: ADHDButtonType.outline,
                  onPressed: () {},
                ),
                ADHDButton(
                  text: 'Destructive',
                  type: ADHDButtonType.destructive,
                  onPressed: () {},
                ),
              ],
            ),
          ),
        ),
      );

      // Assert all buttons are rendered
      expect(find.text('Primary'), findsOneWidget);
      expect(find.text('Secondary'), findsOneWidget);
      expect(find.text('Outline'), findsOneWidget);
      expect(find.text('Destructive'), findsOneWidget);
    });

    testWidgets('should have proper accessibility properties', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ADHDButton(
              text: 'Accessible Button',
              type: ADHDButtonType.primary,
              onPressed: () {},
            ),
          ),
        ),
      );

      // Act
      final semantics = tester.getSemantics(find.byType(ADHDButton));

      // Assert
      expect(semantics.hasAction(SemanticsAction.tap), isTrue);
      expect(semantics.label, contains('Accessible Button'));
    });

    testWidgets('should handle rapid taps correctly', (tester) async {
      // Arrange
      int tapCount = 0;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ADHDButton(
              text: 'Rapid Tap Test',
              type: ADHDButtonType.primary,
              onPressed: () => tapCount++,
            ),
          ),
        ),
      );

      // Act - Rapid taps
      await tester.tap(find.byType(ADHDButton));
      await tester.pump(const Duration(milliseconds: 10));
      await tester.tap(find.byType(ADHDButton));
      await tester.pump(const Duration(milliseconds: 10));
      await tester.tap(find.byType(ADHDButton));

      // Assert - Should handle all taps
      expect(tapCount, equals(3));
    });

    testWidgets('should animate on press', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ADHDButton(
              text: 'Animation Test',
              type: ADHDButtonType.primary,
              onPressed: () {},
            ),
          ),
        ),
      );

      // Act
      await tester.press(find.byType(ADHDButton));
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));

      // Assert - Button should be in pressed state
      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button, isNotNull);
    });

    testWidgets('should maintain minimum size for ADHD accessibility', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ADHDButton(
              text: 'Size Test',
              type: ADHDButtonType.primary,
              onPressed: () {},
            ),
          ),
        ),
      );

      // Act
      final buttonSize = tester.getSize(find.byType(ADHDButton));

      // Assert - Should meet minimum touch target size (44x44 logical pixels)
      expect(buttonSize.height, greaterThanOrEqualTo(44.0));
      expect(buttonSize.width, greaterThanOrEqualTo(44.0));
    });

    testWidgets('should show icon and text together correctly', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ADHDButton(
              text: 'Icon Test',
              icon: Icons.favorite,
              type: ADHDButtonType.primary,
              onPressed: () {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Icon Test'), findsOneWidget);
      expect(find.byIcon(Icons.favorite), findsOneWidget);
      
      // Check that icon comes before text
      final iconFinder = find.byIcon(Icons.favorite);
      final textFinder = find.text('Icon Test');
      
      final iconRect = tester.getRect(iconFinder);
      final textRect = tester.getRect(textFinder);
      
      expect(iconRect.left, lessThan(textRect.left));
    });

    testWidgets('should handle long text gracefully', (tester) async {
      // Arrange
      const longText = 'This is a very long button text that might overflow';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 200, // Constrained width
              child: ADHDButton(
                text: longText,
                type: ADHDButtonType.primary,
                onPressed: () {},
              ),
            ),
          ),
        ),
      );

      // Assert - Should not overflow
      expect(tester.takeException(), isNull);
      expect(find.text(longText), findsOneWidget);
    });

    testWidgets('should support custom colors for themes', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: const ColorScheme.light(
              primary: Colors.purple,
              secondary: Colors.orange,
            ),
          ),
          home: Scaffold(
            body: ADHDButton(
              text: 'Theme Test',
              type: ADHDButtonType.primary,
              onPressed: () {},
            ),
          ),
        ),
      );

      // Act
      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      
      // Assert - Should use theme colors
      expect(button, isNotNull);
    });

    group('Performance Tests', () {
      testWidgets('should render quickly', (tester) async {
        // Arrange
        final stopwatch = Stopwatch()..start();
        
        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ADHDButton(
                text: 'Performance Test',
                type: ADHDButtonType.primary,
                onPressed: () {},
              ),
            ),
          ),
        );
        
        stopwatch.stop();

        // Assert - Should render within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      testWidgets('should handle multiple buttons efficiently', (tester) async {
        // Arrange
        final buttons = List.generate(
          20,
          (index) => ADHDButton(
            text: 'Button $index',
            type: ADHDButtonType.primary,
            onPressed: () {},
          ),
        );

        final stopwatch = Stopwatch()..start();

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ListView(children: buttons),
            ),
          ),
        );

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
        expect(find.byType(ADHDButton), findsNWidgets(20));
      });
    });

    group('Accessibility Tests', () {
      testWidgets('should work with screen readers', (tester) async {
        // Arrange
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ADHDButton(
                text: 'Screen Reader Test',
                type: ADHDButtonType.primary,
                onPressed: () {},
              ),
            ),
          ),
        );

        // Act
        final semantics = tester.getSemantics(find.byType(ADHDButton));

        // Assert
        expect(semantics.hasAction(SemanticsAction.tap), isTrue);
        expect(semantics.isButton, isTrue);
        expect(semantics.label, isNotEmpty);
      });

      testWidgets('should support high contrast mode', (tester) async {
        // Arrange
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              brightness: Brightness.dark,
              colorScheme: const ColorScheme.dark(
                primary: Colors.white,
                onPrimary: Colors.black,
              ),
            ),
            home: Scaffold(
              body: ADHDButton(
                text: 'High Contrast Test',
                type: ADHDButtonType.primary,
                onPressed: () {},
              ),
            ),
          ),
        );

        // Assert - Should render without issues
        expect(find.text('High Contrast Test'), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });
  });
}
