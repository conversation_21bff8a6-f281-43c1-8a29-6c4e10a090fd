import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';

class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  late FirebaseAnalytics _analytics;
  late FirebaseAnalyticsObserver _observer;

  FirebaseAnalyticsObserver get observer => _observer;

  // Initialize analytics
  Future<void> initialize() async {
    _analytics = FirebaseAnalytics.instance;
    _observer = FirebaseAnalyticsObserver(analytics: _analytics);
    
    // Set default parameters
    await _analytics.setDefaultParameters({
      'app_version': '1.0.0',
      'platform': defaultTargetPlatform.name,
    });
  }

  // Set user properties
  Future<void> setUserProperties({
    required String userId,
    String? subscriptionTier,
    int? adhdAssessmentScore,
    List<String>? primaryChallenges,
  }) async {
    await _analytics.setUserId(id: userId);
    
    if (subscriptionTier != null) {
      await _analytics.setUserProperty(
        name: 'subscription_tier',
        value: subscriptionTier,
      );
    }
    
    if (adhdAssessmentScore != null) {
      await _analytics.setUserProperty(
        name: 'adhd_assessment_score',
        value: adhdAssessmentScore.toString(),
      );
    }
    
    if (primaryChallenges != null) {
      await _analytics.setUserProperty(
        name: 'primary_challenges',
        value: primaryChallenges.join(','),
      );
    }
  }

  // Track custom events
  Future<void> trackEvent(String name, [Map<String, dynamic>? parameters]) async {
    try {
      await _analytics.logEvent(
        name: name,
        parameters: parameters?.map(
          (key, value) => MapEntry(key, value?.toString()),
        ),
      );
    } catch (e) {
      debugPrint('Analytics error: $e');
    }
  }

  // Core app events
  Future<void> trackAppOpen() async {
    await trackEvent('app_open');
  }

  Future<void> trackScreenView(String screenName) async {
    await _analytics.logScreenView(screenName: screenName);
  }

  // Onboarding events
  Future<void> trackOnboardingStarted() async {
    await trackEvent('onboarding_started');
  }

  Future<void> trackOnboardingCompleted(int adhdScore, List<String> challenges) async {
    await trackEvent('onboarding_completed', {
      'adhd_assessment_score': adhdScore,
      'primary_challenges': challenges.join(','),
    });
  }

  // Task events
  Future<void> trackTaskCreated(String taskId, int priority, int estimatedMinutes) async {
    await trackEvent('task_created', {
      'task_id': taskId,
      'priority': priority,
      'estimated_minutes': estimatedMinutes,
    });
  }

  Future<void> trackTaskCompleted(String taskId, int actualMinutes, bool hadAIBreakdown) async {
    await trackEvent('task_completed', {
      'task_id': taskId,
      'actual_minutes': actualMinutes,
      'had_ai_breakdown': hadAIBreakdown,
    });
  }

  Future<void> trackAIBreakdownGenerated(String taskId, int stepCount, int difficulty) async {
    await trackEvent('ai_breakdown_generated', {
      'task_id': taskId,
      'step_count': stepCount,
      'difficulty': difficulty,
    });
  }

  // Focus events
  Future<void> trackFocusSessionStarted(String sessionType, int plannedMinutes) async {
    await trackEvent('focus_session_started', {
      'session_type': sessionType,
      'planned_minutes': plannedMinutes,
    });
  }

  Future<void> trackFocusSessionCompleted(
    String sessionType,
    int actualMinutes,
    int interruptions,
  ) async {
    await trackEvent('focus_session_completed', {
      'session_type': sessionType,
      'actual_minutes': actualMinutes,
      'interruptions': interruptions,
    });
  }

  // Energy tracking
  Future<void> trackEnergyLevelSet(int level) async {
    await trackEvent('energy_level_set', {
      'level': level,
    });
  }

  // Subscription events
  Future<void> trackSubscriptionPurchaseAttempt(String packageType, double price) async {
    await trackEvent('subscription_purchase_attempt', {
      'package_type': packageType,
      'price': price,
    });
  }

  Future<void> trackSubscriptionPurchaseSuccess(String packageType) async {
    await trackEvent('subscription_purchase_success', {
      'package_type': packageType,
    });
  }

  Future<void> trackPaywallViewed() async {
    await trackEvent('paywall_viewed');
  }

  Future<void> trackFeatureLimitHit(String feature) async {
    await trackEvent('feature_limit_hit', {
      'feature': feature,
    });
  }

  // Engagement events
  Future<void> trackDailyStreak(int streakDays) async {
    await trackEvent('daily_streak', {
      'streak_days': streakDays,
    });
  }

  Future<void> trackWeeklyReflectionViewed() async {
    await trackEvent('weekly_reflection_viewed');
  }

  Future<void> trackBodyDoublingJoined(String roomId) async {
    await trackEvent('body_doubling_joined', {
      'room_id': roomId,
    });
  }

  // Ad events
  Future<void> trackAdShown(String adType, String placement) async {
    await trackEvent('ad_shown', {
      'ad_type': adType,
      'placement': placement,
    });
  }

  Future<void> trackAdClicked(String adType, String placement) async {
    await trackEvent('ad_clicked', {
      'ad_type': adType,
      'placement': placement,
    });
  }

  // Error tracking
  Future<void> trackError(String error, String? stackTrace) async {
    await trackEvent('app_error', {
      'error': error,
      'stack_trace': stackTrace,
    });
  }

  // Retention events
  Future<void> trackRetentionMilestone(int daysSinceInstall) async {
    await trackEvent('retention_milestone', {
      'days_since_install': daysSinceInstall,
    });
  }
}
