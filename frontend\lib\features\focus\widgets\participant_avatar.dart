import 'package:flutter/material.dart';
import '../providers/body_doubling_provider.dart';

class ParticipantAvatar extends StatelessWidget {
  final BodyDoublingParticipant participant;
  final double size;
  final bool showStatus;

  const ParticipantAvatar({
    super.key,
    required this.participant,
    this.size = 40,
    this.showStatus = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          children: [
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: _getAvatarColors(participant.id),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                border: Border.all(
                  color: _getStatusColor(participant.status),
                  width: 2,
                ),
              ),
              child: Center(
                child: Text(
                  _getInitials(participant.displayName),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: size * 0.4,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            if (showStatus)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: size * 0.3,
                  height: size * 0.3,
                  decoration: BoxDecoration(
                    color: _getStatusColor(participant.status),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: theme.colorScheme.surface,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    _getStatusIcon(participant.status),
                    color: Colors.white,
                    size: size * 0.15,
                  ),
                ),
              ),
          ],
        ),
        if (showStatus) ...[
          const SizedBox(height: 4),
          SizedBox(
            width: size + 10,
            child: Text(
              participant.displayName,
              style: theme.textTheme.labelSmall,
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (participant.currentTask.isNotEmpty) ...[
            const SizedBox(height: 2),
            SizedBox(
              width: size + 10,
              child: Text(
                participant.currentTask,
                style: theme.textTheme.labelSmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ],
      ],
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    } else {
      return '${words[0].substring(0, 1)}${words[1].substring(0, 1)}'.toUpperCase();
    }
  }

  List<Color> _getAvatarColors(String id) {
    // Generate consistent colors based on user ID
    final hash = id.hashCode;
    final colorOptions = [
      [const Color(0xFF6366F1), const Color(0xFF8B5CF6)], // Purple
      [const Color(0xFF10B981), const Color(0xFF059669)], // Green
      [const Color(0xFFF59E0B), const Color(0xFFD97706)], // Orange
      [const Color(0xFFEF4444), const Color(0xFFDC2626)], // Red
      [const Color(0xFF3B82F6), const Color(0xFF2563EB)], // Blue
      [const Color(0xFF8B5A2B), const Color(0xFF92400E)], // Brown
      [const Color(0xFF6B7280), const Color(0xFF4B5563)], // Gray
      [const Color(0xFFEC4899), const Color(0xFFDB2777)], // Pink
    ];
    
    return colorOptions[hash.abs() % colorOptions.length];
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'focusing':
        return const Color(0xFF10B981); // Green
      case 'break':
        return const Color(0xFFF59E0B); // Orange
      case 'away':
        return const Color(0xFF6B7280); // Gray
      default:
        return const Color(0xFF6B7280);
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'focusing':
        return Icons.psychology;
      case 'break':
        return Icons.coffee;
      case 'away':
        return Icons.schedule;
      default:
        return Icons.person;
    }
  }
}
