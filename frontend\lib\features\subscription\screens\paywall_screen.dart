import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/widgets/calm_card.dart';
import '../../../shared/widgets/adhd_button.dart';
import '../../../shared/providers/subscription_provider.dart';
import '../widgets/feature_comparison.dart';
import '../widgets/pricing_cards.dart';
import '../widgets/testimonials.dart';

class PaywallScreen extends StatefulWidget {
  const PaywallScreen({super.key});

  @override
  State<PaywallScreen> createState() => _PaywallScreenState();
}

class _PaywallScreenState extends State<PaywallScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
    ));
    
    _animationController.forward();
    
    // Track paywall view
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SubscriptionProvider>().trackPaywallView();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Consumer<SubscriptionProvider>(
              builder: (context, subscriptionProvider, child) {
                return CustomScrollView(
                  slivers: [
                    _buildAppBar(context),
                    SliverToBoxAdapter(
                      child: Column(
                        children: [
                          const SizedBox(height: 24),
                          _buildHeroSection(context),
                          const SizedBox(height: 32),
                          _buildFeatureComparison(context),
                          const SizedBox(height: 32),
                          _buildPricingCards(context, subscriptionProvider),
                          const SizedBox(height: 32),
                          _buildTestimonials(context),
                          const SizedBox(height: 32),
                          _buildFAQ(context),
                          const SizedBox(height: 32),
                          _buildFooter(context),
                          const SizedBox(height: 100),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      title: const Text('Upgrade to Pro'),
      centerTitle: true,
      floating: true,
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: () => context.pop(),
      ),
      actions: [
        TextButton(
          onPressed: () => _showRestorePurchases(context),
          child: const Text('Restore'),
        ),
      ],
    );
  }

  Widget _buildHeroSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return CalmCard(
      backgroundColor: theme.colorScheme.primaryContainer.withOpacity(0.3),
      child: Column(
        children: [
          // Hero icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(40),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Icon(
              Icons.auto_awesome,
              size: 40,
              color: theme.colorScheme.onPrimary,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Hero title
          Text(
            'Unlock Your Full\nADHD Potential',
            textAlign: TextAlign.center,
            style: theme.textTheme.displaySmall?.copyWith(
              fontWeight: FontWeight.w700,
              color: theme.colorScheme.primary,
              height: 1.2,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Hero subtitle
          Text(
            'Get unlimited AI assistance, advanced focus modes, and personalized insights to transform your productivity.',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.8),
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Social proof
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.secondaryContainer.withOpacity(0.5),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.star,
                  size: 16,
                  color: theme.colorScheme.secondary,
                ),
                const SizedBox(width: 4),
                Text(
                  '4.9/5 from 10,000+ ADHD users',
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: theme.colorScheme.secondary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureComparison(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'What You Get with Pro',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const SizedBox(height: 16),
        const FeatureComparison(),
      ],
    );
  }

  Widget _buildPricingCards(BuildContext context, SubscriptionProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Choose Your Plan',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const SizedBox(height: 16),
        PricingCards(
          isLoading: provider.isLoading,
          onMonthlySelected: () => _handlePurchase(context, provider, true),
          onYearlySelected: () => _handlePurchase(context, provider, false),
        ),
      ],
    );
  }

  Widget _buildTestimonials(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'What ADHD Users Say',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const SizedBox(height: 16),
        const Testimonials(),
      ],
    );
  }

  Widget _buildFAQ(BuildContext context) {
    final theme = Theme.of(context);
    
    return CalmCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Frequently Asked Questions',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildFAQItem(
            context,
            'Can I cancel anytime?',
            'Yes! Cancel anytime from your device settings. No questions asked.',
          ),
          _buildFAQItem(
            context,
            'Is there a free trial?',
            'Your first 7 days are completely free. Cancel before then and pay nothing.',
          ),
          _buildFAQItem(
            context,
            'Will this work for my ADHD?',
            'FocusFlow is designed specifically for ADHD brains. If it doesn\'t help within 30 days, we\'ll refund you.',
          ),
          _buildFAQItem(
            context,
            'What about my data?',
            'Your data stays private and secure. Export or delete it anytime with one tap.',
          ),
        ],
      ),
    );
  }

  Widget _buildFAQItem(BuildContext context, String question, String answer) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            answer,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Text(
            'Join thousands of ADHD users who\'ve transformed their productivity with FocusFlow Pro.',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton(
                onPressed: () => _showPrivacyPolicy(context),
                child: const Text('Privacy Policy'),
              ),
              const Text(' • '),
              TextButton(
                onPressed: () => _showTermsOfService(context),
                child: const Text('Terms of Service'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handlePurchase(BuildContext context, SubscriptionProvider provider, bool isMonthly) async {
    try {
      final success = await provider.purchaseSubscription(isMonthly);
      if (success && mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Welcome to FocusFlow Pro! 🎉'),
            backgroundColor: Theme.of(context).colorScheme.secondary,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchase failed: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showRestorePurchases(BuildContext context) async {
    final provider = context.read<SubscriptionProvider>();
    try {
      await provider.restorePurchases();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Purchases restored successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to restore purchases: $e')),
        );
      }
    }
  }

  void _showPrivacyPolicy(BuildContext context) {
    // Navigate to privacy policy
  }

  void _showTermsOfService(BuildContext context) {
    // Navigate to terms of service
  }
}
