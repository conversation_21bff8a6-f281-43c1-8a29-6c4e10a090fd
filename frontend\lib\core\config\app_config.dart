import 'package:flutter/foundation.dart';

enum Environment {
  development,
  staging,
  production,
}

class AppConfig {
  static const Environment _environment = kDebugMode 
      ? Environment.development 
      : Environment.production;

  // App Information
  static const String appName = 'FocusFlow';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  static const String packageName = 'com.focusflow.adhd_planner';

  // Environment Configuration
  static Environment get environment => _environment;
  static bool get isDevelopment => _environment == Environment.development;
  static bool get isStaging => _environment == Environment.staging;
  static bool get isProduction => _environment == Environment.production;

  // API Configuration
  static String get baseUrl {
    switch (_environment) {
      case Environment.development:
        return 'https://dev-api.focusflow.app';
      case Environment.staging:
        return 'https://staging-api.focusflow.app';
      case Environment.production:
        return 'https://api.focusflow.app';
    }
  }

  // Firebase Configuration
  static String get firebaseProjectId {
    switch (_environment) {
      case Environment.development:
        return 'focusflow-dev';
      case Environment.staging:
        return 'focusflow-staging';
      case Environment.production:
        return 'focusflow-prod';
    }
  }

  // OpenAI Configuration
  static String get openAIApiKey {
    // In production, this should come from secure environment variables
    // For now, using placeholder
    return const String.fromEnvironment(
      'OPENAI_API_KEY',
      defaultValue: 'your-openai-api-key-here',
    );
  }

  static String get openAIModel {
    switch (_environment) {
      case Environment.development:
        return 'gpt-3.5-turbo'; // Cheaper for development
      case Environment.staging:
        return 'gpt-4';
      case Environment.production:
        return 'gpt-4';
    }
  }

  // RevenueCat Configuration
  static String get revenueCatApiKey {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      return const String.fromEnvironment(
        'REVENUECAT_IOS_API_KEY',
        defaultValue: 'appl_your_ios_api_key_here',
      );
    } else {
      return const String.fromEnvironment(
        'REVENUECAT_ANDROID_API_KEY',
        defaultValue: 'goog_your_android_api_key_here',
      );
    }
  }

  // AdMob Configuration
  static String get adMobAppId {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      return isDevelopment 
          ? 'ca-app-pub-3940256099942544~1458002511' // Test app ID
          : 'ca-app-pub-YOUR_ACTUAL_IOS_APP_ID~1234567890';
    } else {
      return isDevelopment
          ? 'ca-app-pub-3940256099942544~3347511713' // Test app ID
          : 'ca-app-pub-YOUR_ACTUAL_ANDROID_APP_ID~1234567890';
    }
  }

  static String get adMobInterstitialId {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      return isDevelopment
          ? 'ca-app-pub-3940256099942544/4411468910' // Test ad unit
          : 'ca-app-pub-YOUR_ACTUAL_IOS_INTERSTITIAL_ID/1234567890';
    } else {
      return isDevelopment
          ? 'ca-app-pub-3940256099942544/1033173712' // Test ad unit
          : 'ca-app-pub-YOUR_ACTUAL_ANDROID_INTERSTITIAL_ID/1234567890';
    }
  }

  static String get adMobRewardedId {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      return isDevelopment
          ? 'ca-app-pub-3940256099942544/1712485313' // Test ad unit
          : 'ca-app-pub-YOUR_ACTUAL_IOS_REWARDED_ID/1234567890';
    } else {
      return isDevelopment
          ? 'ca-app-pub-3940256099942544/5224354917' // Test ad unit
          : 'ca-app-pub-YOUR_ACTUAL_ANDROID_REWARDED_ID/1234567890';
    }
  }

  // Feature Flags
  static bool get enableAnalytics => !isDevelopment;
  static bool get enableCrashlytics => !isDevelopment;
  static bool get enablePerformanceMonitoring => !isDevelopment;
  static bool get enableAds => true;
  static bool get enableBodyDoubling => true;
  static bool get enableWeeklyReflection => true;
  static bool get enableAITaskBreakdown => true;
  static bool get enableOfflineMode => true;
  static bool get enableBiometricAuth => true;
  static bool get enableDataExport => true;

  // App Limits and Quotas
  static int get freeAIRequestsPerDay => 5;
  static int get maxTasksPerDay => 50;
  static int get maxFocusSessionMinutes => 180; // 3 hours
  static int get maxBodyDoublingParticipants => 8;
  static int get maxWeeklyReflections => 1;
  static int get maxAdsPerDay => 2;
  static int get cacheExpirationHours => 24;

  // UI Configuration
  static Duration get animationDuration => const Duration(milliseconds: 300);
  static Duration get longAnimationDuration => const Duration(milliseconds: 600);
  static Duration get splashScreenDuration => const Duration(seconds: 3);
  static Duration get autoSaveInterval => const Duration(seconds: 30);
  static Duration get sessionTimeout => const Duration(minutes: 30);

  // Notification Configuration
  static Duration get defaultNotificationDelay => const Duration(minutes: 5);
  static Duration get focusReminderInterval => const Duration(minutes: 25);
  static Duration get breakReminderInterval => const Duration(minutes: 5);

  // Security Configuration
  static Duration get tokenRefreshInterval => const Duration(hours: 1);
  static int get maxLoginAttempts => 5;
  static Duration get lockoutDuration => const Duration(minutes: 15);
  static bool get requireBiometricForSensitiveActions => isProduction;

  // Performance Configuration
  static int get maxCacheSize => 100 * 1024 * 1024; // 100MB
  static int get maxImageCacheSize => 50 * 1024 * 1024; // 50MB
  static int get maxLogFileSize => 10 * 1024 * 1024; // 10MB
  static int get maxErrorHistorySize => 100;
  static Duration get networkTimeout => const Duration(seconds: 30);

  // ADHD-Specific Configuration
  static int get defaultFocusMinutes => 25;
  static int get defaultBreakMinutes => 5;
  static int get defaultLongBreakMinutes => 15;
  static int get pomodoroSessionsBeforeLongBreak => 4;
  static int get maxTodaysThreeTasks => 3;
  static double get defaultEnergyLevel => 3.0;
  static int get maxTaskBreakdownSteps => 7;
  static int get minTaskBreakdownSteps => 3;

  // Subscription Configuration
  static String get monthlySubscriptionId => 'monthly_pro';
  static String get yearlySubscriptionId => 'yearly_pro';
  static double get monthlyPrice => 6.99;
  static double get yearlyPrice => 39.99;
  static int get freeTrialDays => 7;

  // Support and Legal
  static String get supportEmail => '<EMAIL>';
  static String get privacyPolicyUrl => 'https://focusflow.app/privacy';
  static String get termsOfServiceUrl => 'https://focusflow.app/terms';
  static String get websiteUrl => 'https://focusflow.app';
  static String get feedbackUrl => 'https://focusflow.app/feedback';

  // Social Media
  static String get twitterUrl => 'https://twitter.com/focusflowapp';
  static String get instagramUrl => 'https://instagram.com/focusflowapp';
  static String get linkedinUrl => 'https://linkedin.com/company/focusflow';

  // Development Tools
  static bool get showDebugInfo => isDevelopment;
  static bool get enableLogging => isDevelopment || isStaging;
  static bool get enableTestMode => isDevelopment;
  static bool get skipOnboarding => isDevelopment;
  static bool get mockApiResponses => false;

  // App Store Configuration
  static String get appStoreId => '1234567890';
  static String get playStoreId => 'com.focusflow.adhd_planner';
  static String get appStoreUrl => 'https://apps.apple.com/app/id$appStoreId';
  static String get playStoreUrl => 'https://play.google.com/store/apps/details?id=$playStoreId';

  // Get configuration summary for debugging
  static Map<String, dynamic> getConfigSummary() {
    return {
      'environment': _environment.name,
      'app_version': appVersion,
      'build_number': buildNumber,
      'base_url': baseUrl,
      'firebase_project': firebaseProjectId,
      'features': {
        'analytics': enableAnalytics,
        'crashlytics': enableCrashlytics,
        'performance_monitoring': enablePerformanceMonitoring,
        'ads': enableAds,
        'body_doubling': enableBodyDoubling,
        'weekly_reflection': enableWeeklyReflection,
        'ai_task_breakdown': enableAITaskBreakdown,
        'offline_mode': enableOfflineMode,
        'biometric_auth': enableBiometricAuth,
        'data_export': enableDataExport,
      },
      'limits': {
        'free_ai_requests_per_day': freeAIRequestsPerDay,
        'max_tasks_per_day': maxTasksPerDay,
        'max_focus_session_minutes': maxFocusSessionMinutes,
        'max_ads_per_day': maxAdsPerDay,
      },
      'debug': {
        'show_debug_info': showDebugInfo,
        'enable_logging': enableLogging,
        'enable_test_mode': enableTestMode,
        'skip_onboarding': skipOnboarding,
      },
    };
  }

  // Validate configuration
  static List<String> validateConfig() {
    final errors = <String>[];

    if (openAIApiKey.contains('your-openai-api-key-here')) {
      errors.add('OpenAI API key not configured');
    }

    if (revenueCatApiKey.contains('your_') && isProduction) {
      errors.add('RevenueCat API key not configured for production');
    }

    if (adMobAppId.contains('YOUR_ACTUAL_') && isProduction) {
      errors.add('AdMob configuration not set for production');
    }

    return errors;
  }
}
