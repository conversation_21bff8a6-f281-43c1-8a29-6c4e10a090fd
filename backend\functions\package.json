{"name": "focusflow-functions", "version": "1.0.0", "description": "Firebase Functions for FocusFlow ADHD Planner", "main": "lib/index.js", "scripts": {"build": "tsc", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.5.0", "openai": "^4.20.0", "node-cron": "^3.0.2"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}, "private": true}