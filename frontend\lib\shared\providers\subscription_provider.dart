import 'package:flutter/foundation.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../../core/services/analytics_service.dart';
import '../../core/models/user.dart';

class SubscriptionProvider extends ChangeNotifier {
  final AnalyticsService _analytics = AnalyticsService();
  
  bool _isPro = false;
  bool _isLoading = false;
  String? _error;
  CustomerInfo? _customerInfo;

  // Getters
  bool get isPro => _isPro;
  bool get isLoading => _isLoading;
  String? get error => _error;
  CustomerInfo? get customerInfo => _customerInfo;

  // Initialize RevenueCat
  Future<void> initialize() async {
    try {
      await Purchases.setLogLevel(LogLevel.info);
      
      PurchasesConfiguration configuration;
      if (defaultTargetPlatform == TargetPlatform.android) {
        configuration = PurchasesConfiguration("goog_your_google_play_api_key");
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        configuration = PurchasesConfiguration("appl_your_app_store_api_key");
      } else {
        return;
      }
      
      await Purchases.configure(configuration);
      
      // Check current subscription status
      await _updateCustomerInfo();
      
      // Listen to customer info updates
      Purchases.addCustomerInfoUpdateListener(_onCustomerInfoUpdate);
      
    } catch (e) {
      _error = 'Failed to initialize subscriptions: $e';
      debugPrint(_error);
      notifyListeners();
    }
  }

  // Purchase subscription
  Future<bool> purchaseSubscription(bool isMonthly) async {
    _setLoading(true);
    
    try {
      final offerings = await Purchases.getOfferings();
      final offering = offerings.current;
      
      if (offering == null) {
        throw Exception('No subscription offerings available');
      }
      
      final package = isMonthly 
          ? offering.monthly
          : offering.annual;
      
      if (package == null) {
        throw Exception('Subscription package not available');
      }
      
      // Track purchase attempt
      await _analytics.trackEvent('subscription_purchase_attempt', {
        'package_type': isMonthly ? 'monthly' : 'yearly',
        'price': package.storeProduct.price,
      });
      
      final customerInfo = await Purchases.purchasePackage(package);
      
      // Track successful purchase
      await _analytics.trackEvent('subscription_purchase_success', {
        'package_type': isMonthly ? 'monthly' : 'yearly',
        'transaction_id': customerInfo.originalPurchaseDate?.toIso8601String(),
      });
      
      _onCustomerInfoUpdate(customerInfo);
      return true;
      
    } catch (e) {
      _error = 'Purchase failed: $e';
      
      // Track failed purchase
      await _analytics.trackEvent('subscription_purchase_failed', {
        'error': e.toString(),
      });
      
      debugPrint(_error);
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Restore purchases
  Future<void> restorePurchases() async {
    _setLoading(true);
    
    try {
      final customerInfo = await Purchases.restorePurchases();
      _onCustomerInfoUpdate(customerInfo);
      
      await _analytics.trackEvent('subscription_restore_success');
      
    } catch (e) {
      _error = 'Failed to restore purchases: $e';
      
      await _analytics.trackEvent('subscription_restore_failed', {
        'error': e.toString(),
      });
      
      debugPrint(_error);
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  // Check if user has access to pro features
  bool hasProAccess() {
    return _isPro;
  }

  // Check specific pro feature access
  bool canUseFeature(ProFeature feature) {
    if (_isPro) return true;
    
    // Free tier limits
    switch (feature) {
      case ProFeature.unlimitedAI:
        return false; // Free users get 5 AI breakdowns per day
      case ProFeature.advancedFocus:
        return false; // Free users get basic 25-minute timer only
      case ProFeature.bodyDoubling:
        return false; // Pro feature only
      case ProFeature.weeklyReflection:
        return false; // Pro feature only
      case ProFeature.allThemes:
        return false; // Free users get 2 themes
      case ProFeature.noAds:
        return false; // Free users see respectful ads
      case ProFeature.dataExport:
        return false; // Pro feature only
    }
  }

  // Track paywall view
  Future<void> trackPaywallView() async {
    await _analytics.trackEvent('paywall_viewed');
  }

  // Track feature limit hit
  Future<void> trackFeatureLimitHit(ProFeature feature) async {
    await _analytics.trackEvent('feature_limit_hit', {
      'feature': feature.name,
    });
  }

  // Show upgrade prompt
  void showUpgradePrompt(BuildContext context, ProFeature feature) {
    trackFeatureLimitHit(feature);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getFeatureTitle(feature)),
        content: Text(_getFeatureDescription(feature)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to paywall
              context.push('/paywall');
            },
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  // Private methods
  void _onCustomerInfoUpdate(CustomerInfo customerInfo) {
    _customerInfo = customerInfo;
    _isPro = customerInfo.entitlements.active.containsKey('pro');
    _error = null;
    notifyListeners();
  }

  Future<void> _updateCustomerInfo() async {
    try {
      final customerInfo = await Purchases.getCustomerInfo();
      _onCustomerInfoUpdate(customerInfo);
    } catch (e) {
      debugPrint('Failed to get customer info: $e');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  String _getFeatureTitle(ProFeature feature) {
    switch (feature) {
      case ProFeature.unlimitedAI:
        return 'Unlimited AI Assistance';
      case ProFeature.advancedFocus:
        return 'Advanced Focus Modes';
      case ProFeature.bodyDoubling:
        return 'Body-Doubling Rooms';
      case ProFeature.weeklyReflection:
        return 'Weekly AI Reflection';
      case ProFeature.allThemes:
        return 'All Themes & Customization';
      case ProFeature.noAds:
        return 'Ad-Free Experience';
      case ProFeature.dataExport:
        return 'Data Export';
    }
  }

  String _getFeatureDescription(ProFeature feature) {
    switch (feature) {
      case ProFeature.unlimitedAI:
        return 'Get unlimited AI task breakdowns and personalized suggestions. Free users get 5 per day.';
      case ProFeature.advancedFocus:
        return 'Access custom timers, body-doubling rooms, and advanced focus techniques.';
      case ProFeature.bodyDoubling:
        return 'Join virtual co-working sessions with other ADHD users for accountability.';
      case ProFeature.weeklyReflection:
        return 'Get AI-powered insights about your productivity patterns and personalized suggestions.';
      case ProFeature.allThemes:
        return 'Unlock all themes including dyslexia-friendly and custom color options.';
      case ProFeature.noAds:
        return 'Remove all advertisements for a completely distraction-free experience.';
      case ProFeature.dataExport:
        return 'Export all your data in multiple formats for backup or analysis.';
    }
  }
}

enum ProFeature {
  unlimitedAI,
  advancedFocus,
  bodyDoubling,
  weeklyReflection,
  allThemes,
  noAds,
  dataExport,
}
