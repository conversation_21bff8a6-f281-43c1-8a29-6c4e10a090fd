import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:focusflow/shared/widgets/adhd_button.dart';
import 'package:focusflow/shared/widgets/calm_card.dart';
import 'package:focusflow/shared/widgets/accessibility_text.dart';
import 'package:focusflow/features/today/screens/today_screen.dart';

void main() {
  group('Accessibility Tests', () {
    group('ADHD Button Accessibility', () {
      testWidgets('should have proper semantic labels', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ADHDButton(
                text: 'Start Focus Session',
                icon: Icons.play_arrow,
                type: ADHDButtonType.primary,
                onPressed: () {},
              ),
            ),
          ),
        );

        final semantics = tester.getSemantics(find.byType(ADHDButton));
        
        expect(semantics.hasAction(SemanticsAction.tap), isTrue);
        expect(semantics.label, contains('Start Focus Session'));
        expect(semantics.isButton, isTrue);
      });

      testWidgets('should meet minimum touch target size', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ADHDButton(
                text: 'Small Button',
                type: ADHDButtonType.primary,
                onPressed: () {},
              ),
            ),
          ),
        );

        final buttonSize = tester.getSize(find.byType(ADHDButton));
        
        // WCAG AA requires minimum 44x44 logical pixels
        expect(buttonSize.width, greaterThanOrEqualTo(44.0));
        expect(buttonSize.height, greaterThanOrEqualTo(44.0));
      });

      testWidgets('should work with screen reader navigation', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  ADHDButton(
                    text: 'First Button',
                    type: ADHDButtonType.primary,
                    onPressed: () {},
                  ),
                  ADHDButton(
                    text: 'Second Button',
                    type: ADHDButtonType.secondary,
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          ),
        );

        // Test tab navigation
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();
        
        final firstButton = tester.getSemantics(find.text('First Button'));
        expect(firstButton.hasFlag(SemanticsFlag.isFocused), isTrue);
        
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();
        
        final secondButton = tester.getSemantics(find.text('Second Button'));
        expect(secondButton.hasFlag(SemanticsFlag.isFocused), isTrue);
      });

      testWidgets('should announce state changes', (tester) async {
        bool isLoading = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StatefulBuilder(
                builder: (context, setState) {
                  return ADHDButton(
                    text: 'Loading Test',
                    type: ADHDButtonType.primary,
                    isLoading: isLoading,
                    onPressed: () {
                      setState(() {
                        isLoading = true;
                      });
                    },
                  );
                },
              ),
            ),
          ),
        );

        // Initial state
        expect(find.text('Loading Test'), findsOneWidget);
        
        // Trigger loading state
        await tester.tap(find.byType(ADHDButton));
        await tester.pump();
        
        // Should show loading indicator
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        
        // Should have appropriate semantic label for loading state
        final loadingSemantics = tester.getSemantics(find.byType(ADHDButton));
        expect(loadingSemantics.label, contains('loading'));
      });
    });

    group('Text Accessibility', () {
      testWidgets('should support dyslexia-friendly fonts', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AccessibilityText.dyslexiaFriendly(
                'This text is dyslexia-friendly',
              ),
            ),
          ),
        );

        final textWidget = tester.widget<Text>(find.byType(Text));
        
        // Should have increased letter spacing
        expect(textWidget.style?.letterSpacing, greaterThan(1.0));
        
        // Should have increased line height
        expect(textWidget.style?.height, greaterThan(1.5));
      });

      testWidgets('should support high contrast mode', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              brightness: Brightness.dark,
              colorScheme: const ColorScheme.dark(
                primary: Colors.white,
                onPrimary: Colors.black,
              ),
            ),
            home: Scaffold(
              body: AccessibilityText.highContrast(
                'High contrast text',
              ),
            ),
          ),
        );

        final textWidget = tester.widget<Text>(find.byType(Text));
        
        // Should have high contrast styling
        expect(textWidget.style?.fontWeight, equals(FontWeight.w600));
        expect(textWidget.style?.shadows, isNotEmpty);
      });

      testWidgets('should have proper semantic labels', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AccessibilityText(
                'Important information',
                semanticsLabel: 'This is important information for screen readers',
              ),
            ),
          ),
        );

        final semantics = tester.getSemantics(find.text('Important information'));
        expect(semantics.label, equals('This is important information for screen readers'));
      });
    });

    group('Screen Navigation Accessibility', () {
      testWidgets('should have proper heading hierarchy', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  AccessibilityText.heading('Main Title', level: 1),
                  AccessibilityText.heading('Subtitle', level: 2),
                  AccessibilityText.body('Body text content'),
                ],
              ),
            ),
          ),
        );

        // Check heading semantics
        final h1Semantics = tester.getSemantics(find.text('Main Title'));
        final h2Semantics = tester.getSemantics(find.text('Subtitle'));
        
        expect(h1Semantics.hasFlag(SemanticsFlag.isHeader), isTrue);
        expect(h2Semantics.hasFlag(SemanticsFlag.isHeader), isTrue);
      });

      testWidgets('should support focus management', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  TextField(
                    decoration: const InputDecoration(labelText: 'First Field'),
                  ),
                  TextField(
                    decoration: const InputDecoration(labelText: 'Second Field'),
                  ),
                  ADHDButton(
                    text: 'Submit',
                    type: ADHDButtonType.primary,
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          ),
        );

        // Test focus traversal
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();
        
        // First field should be focused
        expect(find.byType(TextField).first, findsOneWidget);
        
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();
        
        // Second field should be focused
        expect(find.byType(TextField).last, findsOneWidget);
        
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();
        
        // Button should be focused
        final buttonSemantics = tester.getSemantics(find.byType(ADHDButton));
        expect(buttonSemantics.hasFlag(SemanticsFlag.isFocused), isTrue);
      });
    });

    group('Color and Contrast Accessibility', () {
      testWidgets('should meet WCAG contrast requirements', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              colorScheme: const ColorScheme.light(
                primary: Color(0xFF1976D2), // Blue with good contrast
                onPrimary: Colors.white,
                surface: Colors.white,
                onSurface: Color(0xFF212121), // Dark gray with good contrast
              ),
            ),
            home: Scaffold(
              body: Column(
                children: [
                  ADHDButton(
                    text: 'Primary Button',
                    type: ADHDButtonType.primary,
                    onPressed: () {},
                  ),
                  const Text('Regular text content'),
                ],
              ),
            ),
          ),
        );

        // Verify widgets render without accessibility warnings
        expect(tester.takeException(), isNull);
      });

      testWidgets('should work in dark mode', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData.dark(),
            home: Scaffold(
              body: Column(
                children: [
                  ADHDButton(
                    text: 'Dark Mode Button',
                    type: ADHDButtonType.primary,
                    onPressed: () {},
                  ),
                  const CalmCard(
                    child: Text('Dark mode content'),
                  ),
                ],
              ),
            ),
          ),
        );

        // Should render without issues in dark mode
        expect(find.text('Dark Mode Button'), findsOneWidget);
        expect(find.text('Dark mode content'), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });

    group('Motion and Animation Accessibility', () {
      testWidgets('should respect reduced motion preferences', (tester) async {
        // Mock reduced motion preference
        await tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
          const MethodChannel('flutter/platform'),
          (call) async {
            if (call.method == 'SystemChrome.setSystemUIOverlayStyle') {
              return null;
            }
            return null;
          },
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ADHDButton(
                text: 'Animated Button',
                type: ADHDButtonType.primary,
                onPressed: () {},
              ),
            ),
          ),
        );

        // Button should still be functional even with reduced motion
        await tester.tap(find.byType(ADHDButton));
        await tester.pump();
        
        expect(tester.takeException(), isNull);
      });
    });

    group('Form Accessibility', () {
      testWidgets('should have proper form labels and hints', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Task Title',
                      hintText: 'Enter a clear, specific task title',
                      helperText: 'Be specific to help your ADHD brain focus',
                    ),
                  ),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      hintText: 'Optional details about the task',
                    ),
                  ),
                ],
              ),
            ),
          ),
        );

        // Check semantic labels
        final titleFieldSemantics = tester.getSemantics(find.byType(TextFormField).first);
        expect(titleFieldSemantics.label, contains('Task Title'));
        expect(titleFieldSemantics.hint, contains('Enter a clear'));
        
        final descFieldSemantics = tester.getSemantics(find.byType(TextFormField).last);
        expect(descFieldSemantics.label, contains('Description'));
      });

      testWidgets('should announce form validation errors', (tester) async {
        String? errorText;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StatefulBuilder(
                builder: (context, setState) {
                  return TextFormField(
                    decoration: InputDecoration(
                      labelText: 'Required Field',
                      errorText: errorText,
                    ),
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'This field is required';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {
                        errorText = value.isEmpty ? 'This field is required' : null;
                      });
                    },
                  );
                },
              ),
            ),
          ),
        );

        // Trigger validation error
        await tester.enterText(find.byType(TextFormField), '');
        await tester.pump();

        // Should show error message
        expect(find.text('This field is required'), findsOneWidget);
        
        // Error should be announced to screen readers
        final fieldSemantics = tester.getSemantics(find.byType(TextFormField));
        expect(fieldSemantics.hasFlag(SemanticsFlag.hasError), isTrue);
      });
    });

    group('ADHD-Specific Accessibility', () {
      testWidgets('should have clear visual hierarchy', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  AccessibilityText.heading('Today\'s Three', level: 1),
                  const CalmCard(
                    child: Column(
                      children: [
                        AccessibilityText.heading('Priority Tasks', level: 2),
                        AccessibilityText.body('Focus on these three tasks today'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );

        // Should have clear heading hierarchy
        final h1 = tester.getSemantics(find.text('Today\'s Three'));
        final h2 = tester.getSemantics(find.text('Priority Tasks'));
        
        expect(h1.hasFlag(SemanticsFlag.isHeader), isTrue);
        expect(h2.hasFlag(SemanticsFlag.isHeader), isTrue);
      });

      testWidgets('should provide clear feedback for actions', (tester) async {
        bool taskCompleted = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StatefulBuilder(
                builder: (context, setState) {
                  return Column(
                    children: [
                      Text(taskCompleted ? 'Task completed!' : 'Task pending'),
                      ADHDButton(
                        text: taskCompleted ? 'Completed' : 'Mark Complete',
                        type: taskCompleted 
                            ? ADHDButtonType.secondary 
                            : ADHDButtonType.primary,
                        onPressed: taskCompleted ? null : () {
                          setState(() {
                            taskCompleted = true;
                          });
                        },
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );

        // Initial state
        expect(find.text('Task pending'), findsOneWidget);
        expect(find.text('Mark Complete'), findsOneWidget);
        
        // Complete task
        await tester.tap(find.byType(ADHDButton));
        await tester.pump();
        
        // Should provide clear feedback
        expect(find.text('Task completed!'), findsOneWidget);
        expect(find.text('Completed'), findsOneWidget);
        
        // Button should be disabled after completion
        final buttonSemantics = tester.getSemantics(find.byType(ADHDButton));
        expect(buttonSemantics.hasFlag(SemanticsFlag.hasEnabledState), isTrue);
        expect(buttonSemantics.hasFlag(SemanticsFlag.isEnabled), isFalse);
      });
    });
  });
}
