import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';

import '../features/auth/screens/login_screen.dart';
import '../features/auth/screens/signup_screen.dart';
import '../features/onboarding/screens/welcome_screen.dart';
import '../features/onboarding/screens/adhd_assessment_screen.dart';
import '../features/onboarding/screens/goal_setting_screen.dart';
import '../features/today/screens/today_screen.dart';
import '../features/today/screens/task_breakdown_screen.dart';
import '../features/focus/screens/focus_mode_screen.dart';
import '../features/focus/screens/body_doubling_screen.dart';
import '../features/tasks/screens/task_list_screen.dart';
import '../features/tasks/screens/add_task_screen.dart';
import '../features/reflection/screens/weekly_reflection_screen.dart';
import '../features/profile/screens/profile_screen.dart';
import '../features/profile/screens/settings_screen.dart';
import '../features/subscription/screens/paywall_screen.dart';
import '../shared/widgets/main_navigation.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/today',
    routes: [
      // Authentication routes
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/signup',
        builder: (context, state) => const SignupScreen(),
      ),
      
      // Onboarding routes
      GoRoute(
        path: '/welcome',
        builder: (context, state) => const WelcomeScreen(),
      ),
      GoRoute(
        path: '/assessment',
        builder: (context, state) => const ADHDAssessmentScreen(),
      ),
      GoRoute(
        path: '/goals',
        builder: (context, state) => const GoalSettingScreen(),
      ),
      
      // Main app routes with bottom navigation
      ShellRoute(
        builder: (context, state, child) => MainNavigation(child: child),
        routes: [
          GoRoute(
            path: '/today',
            builder: (context, state) => const TodayScreen(),
            routes: [
              GoRoute(
                path: 'breakdown/:taskId',
                builder: (context, state) => TaskBreakdownScreen(
                  taskId: state.pathParameters['taskId']!,
                ),
              ),
            ],
          ),
          GoRoute(
            path: '/focus',
            builder: (context, state) => const FocusModeScreen(),
            routes: [
              GoRoute(
                path: 'body-doubling',
                builder: (context, state) => const BodyDoublingScreen(),
              ),
            ],
          ),
          GoRoute(
            path: '/tasks',
            builder: (context, state) => const TaskListScreen(),
            routes: [
              GoRoute(
                path: 'add',
                builder: (context, state) => const AddTaskScreen(),
              ),
            ],
          ),
          GoRoute(
            path: '/reflection',
            builder: (context, state) => const WeeklyReflectionScreen(),
          ),
          GoRoute(
            path: '/profile',
            builder: (context, state) => const ProfileScreen(),
            routes: [
              GoRoute(
                path: 'settings',
                builder: (context, state) => const SettingsScreen(),
              ),
            ],
          ),
        ],
      ),
      
      // Subscription routes
      GoRoute(
        path: '/paywall',
        builder: (context, state) => const PaywallScreen(),
      ),
    ],
  );
}
