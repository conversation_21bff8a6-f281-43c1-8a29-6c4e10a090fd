import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/theme/app_theme.dart';
import '../../core/models/user.dart';

class ThemeProvider extends ChangeNotifier {
  AppThemeType _currentTheme = AppThemeType.light;
  ThemeMode _themeMode = ThemeMode.light;
  
  AppThemeType get currentTheme => _currentTheme;
  ThemeMode get themeMode => _themeMode;
  
  ThemeData get lightTheme => AppTheme.lightTheme;
  ThemeData get darkTheme => AppTheme.darkTheme;

  ThemeProvider() {
    _loadThemeFromPreferences();
  }

  Future<void> _loadThemeFromPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeIndex = prefs.getInt('theme_type') ?? 0;
      final themeModeIndex = prefs.getInt('theme_mode') ?? 0;
      
      _currentTheme = AppThemeType.values[themeIndex];
      _themeMode = ThemeMode.values[themeModeIndex];
      
      notifyListeners();
    } catch (e) {
      print('Failed to load theme preferences: $e');
    }
  }

  Future<void> setTheme(AppThemeType theme) async {
    _currentTheme = theme;
    
    // Update theme mode based on theme type
    switch (theme) {
      case AppThemeType.light:
      case AppThemeType.pastel:
      case AppThemeType.dyslexia:
        _themeMode = ThemeMode.light;
        break;
      case AppThemeType.dark:
        _themeMode = ThemeMode.dark;
        break;
    }
    
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('theme_type', theme.index);
      await prefs.setInt('theme_mode', _themeMode.index);
    } catch (e) {
      print('Failed to save theme preferences: $e');
    }
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('theme_mode', mode.index);
    } catch (e) {
      print('Failed to save theme mode: $e');
    }
  }

  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get isLightMode => _themeMode == ThemeMode.light;
  bool get isSystemMode => _themeMode == ThemeMode.system;
}
