import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // ADHD-friendly color palette
  static const Color primaryBlue = Color(0xFF4A90E2);
  static const Color secondaryGreen = Color(0xFF7ED321);
  static const Color accentOrange = Color(0xFFF5A623);
  static const Color successGreen = Color(0xFF28A745);
  static const Color warningYellow = Color(0xFFFFC107);
  static const Color errorRed = Color(0xFFDC3545);
  
  // Neutral colors
  static const Color lightBackground = Color(0xFFF8F9FA);
  static const Color darkBackground = Color(0xFF1A1A1A);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color darkSurface = Color(0xFF2D2D2D);
  static const Color lightText = Color(0xFF212529);
  static const Color darkText = Color(0xFFE9ECEF);
  static const Color subtleGray = Color(0xFF6C757D);
  
  // Pastel theme colors (for calming effect)
  static const Color pastelPink = Color(0xFFF8E8E8);
  static const Color pastelBlue = Color(0xFFE8F4F8);
  static const Color pastelGreen = Color(0xFFE8F8E8);
  static const Color pastelPurple = Color(0xFFF0E8F8);

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: const ColorScheme.light(
        primary: primaryBlue,
        secondary: secondaryGreen,
        tertiary: accentOrange,
        surface: lightSurface,
        background: lightBackground,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: lightText,
        onBackground: lightText,
      ),
      textTheme: _buildTextTheme(lightText),
      elevatedButtonTheme: _buildElevatedButtonTheme(false),
      cardTheme: _buildCardTheme(false),
      appBarTheme: _buildAppBarTheme(false),
      bottomNavigationBarTheme: _buildBottomNavTheme(false),
      inputDecorationTheme: _buildInputDecorationTheme(false),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: const ColorScheme.dark(
        primary: primaryBlue,
        secondary: secondaryGreen,
        tertiary: accentOrange,
        surface: darkSurface,
        background: darkBackground,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: darkText,
        onBackground: darkText,
      ),
      textTheme: _buildTextTheme(darkText),
      elevatedButtonTheme: _buildElevatedButtonTheme(true),
      cardTheme: _buildCardTheme(true),
      appBarTheme: _buildAppBarTheme(true),
      bottomNavigationBarTheme: _buildBottomNavTheme(true),
      inputDecorationTheme: _buildInputDecorationTheme(true),
    );
  }

  static ThemeData get pastelTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: const ColorScheme.light(
        primary: Color(0xFF8B9DC3),
        secondary: Color(0xFFA8D8A8),
        tertiary: Color(0xFFDDB892),
        surface: pastelPink,
        background: Color(0xFFFAF8F8),
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Color(0xFF4A4A4A),
        onBackground: Color(0xFF4A4A4A),
      ),
      textTheme: _buildTextTheme(const Color(0xFF4A4A4A)),
      elevatedButtonTheme: _buildElevatedButtonTheme(false),
      cardTheme: _buildCardTheme(false),
      appBarTheme: _buildAppBarTheme(false),
      bottomNavigationBarTheme: _buildBottomNavTheme(false),
      inputDecorationTheme: _buildInputDecorationTheme(false),
    );
  }

  static TextTheme _buildTextTheme(Color textColor) {
    return GoogleFonts.interTextTheme().copyWith(
      // Large text sizes for ADHD accessibility
      displayLarge: GoogleFonts.inter(
        fontSize: 32,
        fontWeight: FontWeight.w700,
        color: textColor,
        height: 1.2,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: 28,
        fontWeight: FontWeight.w600,
        color: textColor,
        height: 1.3,
      ),
      displaySmall: GoogleFonts.inter(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: textColor,
        height: 1.3,
      ),
      headlineLarge: GoogleFonts.inter(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        color: textColor,
        height: 1.4,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w500,
        color: textColor,
        height: 1.4,
      ),
      headlineSmall: GoogleFonts.inter(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: textColor,
        height: 1.4,
      ),
      bodyLarge: GoogleFonts.inter(
        fontSize: 18, // Larger than typical for ADHD readability
        fontWeight: FontWeight.w400,
        color: textColor,
        height: 1.6,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: textColor,
        height: 1.6,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: textColor.withOpacity(0.8),
        height: 1.6,
      ),
      labelLarge: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: textColor,
        height: 1.4,
      ),
      labelMedium: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: textColor,
        height: 1.4,
      ),
      labelSmall: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: textColor.withOpacity(0.8),
        height: 1.4,
      ),
    );
  }

  static ElevatedButtonThemeData _buildElevatedButtonTheme(bool isDark) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        textStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  static CardTheme _buildCardTheme(bool isDark) {
    return CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    );
  }

  static AppBarTheme _buildAppBarTheme(bool isDark) {
    return AppBarTheme(
      elevation: 0,
      centerTitle: true,
      titleTextStyle: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: isDark ? darkText : lightText,
      ),
      backgroundColor: isDark ? darkBackground : lightBackground,
      foregroundColor: isDark ? darkText : lightText,
    );
  }

  static BottomNavigationBarThemeData _buildBottomNavTheme(bool isDark) {
    return BottomNavigationBarThemeData(
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedItemColor: primaryBlue,
      unselectedItemColor: subtleGray,
      backgroundColor: isDark ? darkSurface : lightSurface,
      selectedLabelStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  static InputDecorationTheme _buildInputDecorationTheme(bool isDark) {
    return InputDecorationTheme(
      filled: true,
      fillColor: isDark ? darkSurface : lightSurface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: subtleGray.withOpacity(0.3)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: subtleGray.withOpacity(0.3)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: primaryBlue, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      hintStyle: GoogleFonts.inter(
        color: subtleGray,
        fontSize: 16,
      ),
    );
  }
}
