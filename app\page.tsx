"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { CheckCircle2, Circle, Zap, Battery, BatteryLow, Play, Pause, RotateCcw } from "lucide-react"

interface Task {
  id: string
  title: string
  completed: boolean
  energy: "high" | "medium" | "low"
  estimatedTime: number
}

interface EnergyLevel {
  level: "high" | "medium" | "low"
  label: string
  color: string
  icon: any
}

export default function FocusFlowApp() {
  const [tasks, setTasks] = useState<Task[]>([
    { id: "1", title: "Review morning emails", completed: false, energy: "medium", estimatedTime: 15 },
    { id: "2", title: "Write project proposal draft", completed: false, energy: "high", estimatedTime: 45 },
    { id: "3", title: "Organize desk workspace", completed: false, energy: "low", estimatedTime: 10 },
  ])

  const [currentEnergy, setCurrentEnergy] = useState<"high" | "medium" | "low">("medium")
  const [focusMode, setFocusMode] = useState(false)
  const [focusTime, setFocusTime] = useState(25 * 60) // 25 minutes in seconds
  const [isRunning, setIsRunning] = useState(false)
  const [completedToday, setCompletedToday] = useState(0)

  const energyLevels: EnergyLevel[] = [
    { level: "high", label: "High Energy", color: "bg-green-100 text-green-800 border-green-200", icon: Zap },
    {
      level: "medium",
      label: "Medium Energy",
      color: "bg-yellow-100 text-yellow-800 border-yellow-200",
      icon: Battery,
    },
    { level: "low", label: "Low Energy", color: "bg-blue-100 text-blue-800 border-blue-200", icon: BatteryLow },
  ]

  // Focus timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isRunning && focusTime > 0) {
      interval = setInterval(() => {
        setFocusTime((time) => time - 1)
      }, 1000)
    } else if (focusTime === 0) {
      setIsRunning(false)
      setFocusMode(false)
      setFocusTime(25 * 60)
    }
    return () => clearInterval(interval)
  }, [isRunning, focusTime])

  const toggleTask = (taskId: string) => {
    setTasks((prev) =>
      prev.map((task) => {
        if (task.id === taskId) {
          const newCompleted = !task.completed
          if (newCompleted && !task.completed) {
            setCompletedToday((prev) => prev + 1)
          } else if (!newCompleted && task.completed) {
            setCompletedToday((prev) => prev - 1)
          }
          return { ...task, completed: newCompleted }
        }
        return task
      }),
    )
  }

  const getRecommendedTasks = () => {
    return tasks.filter((task) => !task.completed && task.energy === currentEnergy)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const startFocusMode = () => {
    setFocusMode(true)
    setIsRunning(true)
  }

  const toggleTimer = () => {
    setIsRunning(!isRunning)
  }

  const resetTimer = () => {
    setIsRunning(false)
    setFocusTime(25 * 60)
  }

  const currentEnergyData = energyLevels.find((e) => e.level === currentEnergy)!
  const completionPercentage = (completedToday / tasks.length) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-4xl font-bold text-gray-800">FocusFlow</h1>
          <p className="text-gray-600">Your ADHD-friendly task companion</p>
        </div>

        {/* Progress Overview */}
        <Card className="border-2 border-indigo-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-green-600" />
              Today's Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Tasks completed</span>
                <span className="font-semibold">
                  {completedToday} of {tasks.length}
                </span>
              </div>
              <Progress value={completionPercentage} className="h-3" />
              {completedToday > 0 && (
                <div className="text-center">
                  <span className="text-2xl">🎉</span>
                  <p className="text-sm text-green-600 font-medium">Great job! Keep the momentum going!</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Energy Tracker */}
          <Card className="border-2 border-purple-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <currentEnergyData.icon className="h-5 w-5" />
                Energy Level
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="text-sm text-gray-600">How are you feeling right now?</p>
                <div className="grid grid-cols-1 gap-2">
                  {energyLevels.map((energy) => (
                    <Button
                      key={energy.level}
                      variant={currentEnergy === energy.level ? "default" : "outline"}
                      className={`justify-start ${currentEnergy === energy.level ? "ring-2 ring-offset-2 ring-indigo-500" : ""}`}
                      onClick={() => setCurrentEnergy(energy.level)}
                    >
                      <energy.icon className="h-4 w-4 mr-2" />
                      {energy.label}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Focus Mode */}
          <Card className="border-2 border-orange-200">
            <CardHeader>
              <CardTitle>Focus Mode</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {!focusMode ? (
                  <div className="text-center space-y-3">
                    <p className="text-sm text-gray-600">Ready for a focused work session?</p>
                    <Button onClick={startFocusMode} className="w-full">
                      <Play className="h-4 w-4 mr-2" />
                      Start 25-min Focus Session
                    </Button>
                  </div>
                ) : (
                  <div className="text-center space-y-4">
                    <div className="text-3xl font-mono font-bold text-orange-600">{formatTime(focusTime)}</div>
                    <div className="flex gap-2 justify-center">
                      <Button onClick={toggleTimer} variant="outline" size="sm">
                        {isRunning ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                      <Button onClick={resetTimer} variant="outline" size="sm">
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500">Stay focused! You've got this! 💪</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Today's Three Tasks */}
        <Card className="border-2 border-green-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Circle className="h-5 w-5 text-green-600" />
              Today's Three
            </CardTitle>
            <p className="text-sm text-gray-600">Focus on just these 3 tasks today. You've got this!</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {tasks.map((task) => {
                const energyData = energyLevels.find((e) => e.level === task.energy)!
                const isRecommended = getRecommendedTasks().includes(task)

                return (
                  <div
                    key={task.id}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      task.completed
                        ? "bg-green-50 border-green-200"
                        : isRecommended
                          ? "bg-yellow-50 border-yellow-300 ring-2 ring-yellow-200"
                          : "bg-white border-gray-200"
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <Button variant="ghost" size="sm" onClick={() => toggleTask(task.id)} className="p-0 h-auto">
                        {task.completed ? (
                          <CheckCircle2 className="h-6 w-6 text-green-600" />
                        ) : (
                          <Circle className="h-6 w-6 text-gray-400" />
                        )}
                      </Button>

                      <div className="flex-1">
                        <p className={`font-medium ${task.completed ? "line-through text-gray-500" : "text-gray-800"}`}>
                          {task.title}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className={energyData.color}>
                            <energyData.icon className="h-3 w-3 mr-1" />
                            {energyData.label}
                          </Badge>
                          <span className="text-xs text-gray-500">{task.estimatedTime} min</span>
                          {isRecommended && (
                            <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">Recommended now</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {getRecommendedTasks().length > 0 && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800">
                  💡 Based on your current energy level, we recommend starting with the highlighted tasks!
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
