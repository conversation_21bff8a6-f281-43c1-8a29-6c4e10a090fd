# FocusFlow Setup Guide

This guide will help you set up the FocusFlow Flutter app locally for development and testing.

## 🚀 Quick Start

### 1. Prerequisites
- **Flutter SDK**: Version 3.16.0 or higher
- **Dart SDK**: Version 3.2.0 or higher
- **Android Studio** or **VS Code** with Flutter extensions
- **Xcode** (for iOS development on macOS)
- **Firebase CLI** (optional, for backend deployment)

### 2. Installation Steps

#### Clone and Setup
\`\`\`bash
# Clone the repository
git clone <your-repo-url>
cd focusflow/frontend

# Install dependencies
flutter pub get

# Generate necessary files
flutter packages pub run build_runner build
\`\`\`

#### Firebase Configuration
1. **Create Firebase Project**
   - Go to [Firebase Console](https://console.firebase.google.com)
   - Create a new project named "focusflow-adhd"
   - Enable Authentication, Firestore, Analytics, and Crashlytics

2. **Download Configuration Files**
   - **Android**: Download `google-services.json` → Place in `android/app/`
   - **iOS**: Download `GoogleService-Info.plist` → Place in `ios/Runner/`

3. **Update Firebase Options**
   - Replace placeholder values in `lib/app/firebase_options.dart`
   - Use your actual Firebase project configuration

4. **Configure Firestore Rules**
   \`\`\`javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /users/{userId} {
         allow read, write: if request.auth != null && request.auth.uid == userId;
       }
       match /tasks/{taskId} {
         allow read, write: if request.auth != null && 
           resource.data.userId == request.auth.uid;
       }
       match /ai_cache/{document} {
         allow read, write: if request.auth != null;
       }
       match /daily_energy/{document} {
         allow read, write: if request.auth != null;
       }
     }
   }
   \`\`\`

### 3. Environment Setup

#### OpenAI Integration (Optional)
Create `.env` file in project root:
\`\`\`
OPENAI_API_KEY=your_openai_api_key_here
\`\`\`

#### Android Setup
1. **Minimum SDK**: Ensure `android/app/build.gradle` has `minSdkVersion 21`
2. **Permissions**: Already configured in `android/app/src/main/AndroidManifest.xml`

#### iOS Setup
1. **Minimum Version**: iOS 12.0+ (configured in `ios/Runner/Info.plist`)
2. **Permissions**: Already configured for notifications and background processing

### 4. Running the App

#### Development Mode
\`\`\`bash
# Run on connected device/emulator
flutter run

# Run with specific flavor
flutter run --flavor development
flutter run --flavor production

# Run with verbose logging
flutter run --verbose
\`\`\`

#### Debug vs Release
\`\`\`bash
# Debug build (default)
flutter run

# Release build
flutter run --release

# Profile build (for performance testing)
flutter run --profile
\`\`\`

## 🔧 Development Workflow

### Code Generation
\`\`\`bash
# Generate model files and other generated code
flutter packages pub run build_runner build

# Watch for changes and auto-generate
flutter packages pub run build_runner watch
\`\`\`

### Testing
\`\`\`bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/

# Run specific test file
flutter test test/models/task_test.dart

# Run tests with coverage
flutter test --coverage
\`\`\`

### Debugging
\`\`\`bash
# Enable Flutter Inspector
flutter run --debug

# Hot reload: Press 'r' in terminal
# Hot restart: Press 'R' in terminal
# Quit: Press 'q' in terminal
\`\`\`

## 📱 Building for Production

### Android
\`\`\`bash
# Build APK
flutter build apk --release

# Build App Bundle (recommended for Play Store)
flutter build appbundle --release

# Build with specific flavor
flutter build appbundle --release --flavor production
\`\`\`

### iOS
\`\`\`bash
# Build for iOS
flutter build ios --release

# Build for App Store
flutter build ipa --release
\`\`\`

## 🛠️ Troubleshooting

### Common Issues

#### 1. Firebase Configuration
**Error**: `FirebaseOptions have not been configured`
**Solution**: Ensure `firebase_options.dart` has correct configuration values

#### 2. Dependency Conflicts
**Error**: Version conflicts in `pubspec.yaml`
**Solution**: 
\`\`\`bash
flutter clean
flutter pub get
flutter pub deps
\`\`\`

#### 3. Build Failures
**Error**: Build fails with Gradle/Xcode errors
**Solution**:
\`\`\`bash
# Android
cd android && ./gradlew clean && cd ..
flutter clean && flutter pub get

# iOS
cd ios && rm -rf Pods Podfile.lock && pod install && cd ..
flutter clean && flutter pub get
\`\`\`

#### 4. Hot Reload Issues
**Error**: Hot reload not working
**Solution**:
\`\`\`bash
# Restart with hot reload enabled
flutter run --hot
\`\`\`

### Performance Optimization

#### 1. Reduce App Size
\`\`\`bash
# Build with tree shaking
flutter build apk --release --tree-shake-icons

# Analyze bundle size
flutter build apk --analyze-size
\`\`\`

#### 2. Memory Management
- Use `const` constructors where possible
- Dispose controllers in `dispose()` methods
- Use `ListView.builder` for large lists

#### 3. Network Optimization
- Enable caching for images and API responses
- Use compression for large data transfers
- Implement offline-first architecture

## 🔐 Security Considerations

### API Keys
- Never commit API keys to version control
- Use environment variables or secure storage
- Rotate keys regularly

### User Data
- Encrypt sensitive data locally
- Use HTTPS for all network requests
- Implement proper authentication flows

### App Security
- Enable code obfuscation for release builds
- Use certificate pinning for API calls
- Implement proper input validation

## 📊 Analytics & Monitoring

### Firebase Analytics
Events are automatically tracked:
- `task_completed`
- `energy_level_updated`
- `ai_breakdown_generated`
- `focus_session_started`

### Crashlytics
Crashes are automatically reported in release builds.

### Performance Monitoring
Monitor app performance through Firebase Performance.

## 🚀 Deployment

### App Store Deployment
1. **iOS App Store**
   - Build with `flutter build ipa --release`
   - Upload via Xcode or Transporter
   - Submit for review

2. **Google Play Store**
   - Build with `flutter build appbundle --release`
   - Upload via Google Play Console
   - Submit for review

### CI/CD Pipeline
GitHub Actions workflow is configured in `.github/workflows/deploy.yml`

## 📞 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Search existing GitHub issues
3. Create a new issue with detailed information
4. Join our Discord community for help

---

**Happy coding! 🎉**
