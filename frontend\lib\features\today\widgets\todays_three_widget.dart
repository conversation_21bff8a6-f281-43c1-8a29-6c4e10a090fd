import 'package:flutter/material.dart';

import '../../../core/models/task.dart';
import '../../../shared/widgets/calm_card.dart';

class TodaysThreeWidget extends StatelessWidget {
  final List<Task> tasks;
  final Function(Task) onTaskTap;
  final Function(Task) onTaskComplete;
  final Function(int, int) onTaskReorder;

  const TodaysThreeWidget({
    super.key,
    required this.tasks,
    required this.onTaskTap,
    required this.onTaskComplete,
    required this.onTaskReorder,
  });

  @override
  Widget build(BuildContext context) {
    if (tasks.isEmpty) {
      return _buildEmptyState(context);
    }

    return ReorderableListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: tasks.length,
      onReorder: onTaskReorder,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return _buildTaskCard(context, task, index);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return CalmCard(
      backgroundColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
      child: Column(
        children: [
          Icon(
            Icons.task_alt,
            size: 48,
            color: theme.colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No tasks for today yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add up to 3 tasks to focus on today.\nKeeping it simple helps you succeed!',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskCard(BuildContext context, Task task, int index) {
    final theme = Theme.of(context);
    final isCompleted = task.isCompleted;
    
    return Container(
      key: ValueKey(task.id),
      margin: const EdgeInsets.only(bottom: 12),
      child: CalmCard(
        onTap: () => onTaskTap(task),
        backgroundColor: isCompleted 
            ? theme.colorScheme.secondaryContainer.withOpacity(0.3)
            : theme.colorScheme.surface,
        showBorder: !isCompleted,
        borderColor: _getPriorityColor(theme, task.priority),
        child: Row(
          children: [
            // Priority indicator
            Container(
              width: 4,
              height: 40,
              decoration: BoxDecoration(
                color: _getPriorityColor(theme, task.priority),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 16),
            
            // Task content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        '${index + 1}.',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          task.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            decoration: isCompleted 
                                ? TextDecoration.lineThrough
                                : null,
                            color: isCompleted 
                                ? theme.colorScheme.onSurface.withOpacity(0.6)
                                : null,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // Task metadata
                  Row(
                    children: [
                      // Time estimate
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primaryContainer.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.schedule,
                              size: 14,
                              color: theme.colorScheme.primary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              task.estimatedTimeLabel,
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(width: 8),
                      
                      // AI breakdown indicator
                      if (task.aiBreakdown != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.secondaryContainer.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.auto_awesome,
                                size: 14,
                                color: theme.colorScheme.secondary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${task.aiBreakdown!.steps.length} steps',
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color: theme.colorScheme.secondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      
                      const Spacer(),
                      
                      // Priority label
                      Text(
                        task.priorityLabel,
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: _getPriorityColor(theme, task.priority),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Complete button
            GestureDetector(
              onTap: () => onTaskComplete(task),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: isCompleted 
                      ? theme.colorScheme.secondary
                      : Colors.transparent,
                  border: Border.all(
                    color: isCompleted 
                        ? theme.colorScheme.secondary
                        : theme.colorScheme.outline,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: isCompleted
                    ? Icon(
                        Icons.check,
                        size: 20,
                        color: theme.colorScheme.onSecondary,
                      )
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getPriorityColor(ThemeData theme, TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return const Color(0xFFDC3545); // Red
      case TaskPriority.medium:
        return const Color(0xFFF5A623); // Orange
      case TaskPriority.low:
        return theme.colorScheme.secondary; // Green
    }
  }
}
