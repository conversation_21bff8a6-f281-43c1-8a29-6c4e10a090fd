import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';

class EncryptionService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static Encrypter? _encrypter;
  static IV? _iv;

  /// Initialize encryption service
  static Future<void> initialize() async {
    try {
      await _initializeEncryption();
    } catch (e) {
      debugPrint('Encryption initialization error: $e');
    }
  }

  /// Initialize encryption keys
  static Future<void> _initializeEncryption() async {
    // Try to get existing key
    String? keyString = await _storage.read(key: 'encryption_key');
    
    if (keyString == null) {
      // Generate new key
      final key = Key.fromSecureRandom(32);
      keyString = key.base64;
      await _storage.write(key: 'encryption_key', value: keyString);
    }

    final key = Key.fromBase64(keyString);
    _encrypter = Encrypter(AES(key));
    
    // Generate or retrieve IV
    String? ivString = await _storage.read(key: 'encryption_iv');
    if (ivString == null) {
      _iv = IV.fromSecureRandom(16);
      await _storage.write(key: 'encryption_iv', value: _iv!.base64);
    } else {
      _iv = IV.fromBase64(ivString);
    }
  }

  /// Encrypt sensitive text data
  static Future<String?> encryptText(String plainText) async {
    try {
      if (_encrypter == null || _iv == null) {
        await _initializeEncryption();
      }
      
      final encrypted = _encrypter!.encrypt(plainText, iv: _iv!);
      return encrypted.base64;
    } catch (e) {
      debugPrint('Text encryption error: $e');
      return null;
    }
  }

  /// Decrypt sensitive text data
  static Future<String?> decryptText(String encryptedText) async {
    try {
      if (_encrypter == null || _iv == null) {
        await _initializeEncryption();
      }
      
      final encrypted = Encrypted.fromBase64(encryptedText);
      final decrypted = _encrypter!.decrypt(encrypted, iv: _iv!);
      return decrypted;
    } catch (e) {
      debugPrint('Text decryption error: $e');
      return null;
    }
  }

  /// Encrypt JSON data
  static Future<String?> encryptJson(Map<String, dynamic> data) async {
    try {
      final jsonString = jsonEncode(data);
      return await encryptText(jsonString);
    } catch (e) {
      debugPrint('JSON encryption error: $e');
      return null;
    }
  }

  /// Decrypt JSON data
  static Future<Map<String, dynamic>?> decryptJson(String encryptedData) async {
    try {
      final decryptedString = await decryptText(encryptedData);
      if (decryptedString == null) return null;
      
      return jsonDecode(decryptedString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('JSON decryption error: $e');
      return null;
    }
  }

  /// Store encrypted data in secure storage
  static Future<bool> storeSecureData(String key, String data) async {
    try {
      final encryptedData = await encryptText(data);
      if (encryptedData == null) return false;
      
      await _storage.write(key: key, value: encryptedData);
      return true;
    } catch (e) {
      debugPrint('Secure storage error: $e');
      return false;
    }
  }

  /// Retrieve and decrypt data from secure storage
  static Future<String?> getSecureData(String key) async {
    try {
      final encryptedData = await _storage.read(key: key);
      if (encryptedData == null) return null;
      
      return await decryptText(encryptedData);
    } catch (e) {
      debugPrint('Secure retrieval error: $e');
      return null;
    }
  }

  /// Store encrypted JSON in secure storage
  static Future<bool> storeSecureJson(String key, Map<String, dynamic> data) async {
    try {
      final jsonString = jsonEncode(data);
      return await storeSecureData(key, jsonString);
    } catch (e) {
      debugPrint('Secure JSON storage error: $e');
      return false;
    }
  }

  /// Retrieve and decrypt JSON from secure storage
  static Future<Map<String, dynamic>?> getSecureJson(String key) async {
    try {
      final jsonString = await getSecureData(key);
      if (jsonString == null) return null;
      
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Secure JSON retrieval error: $e');
      return null;
    }
  }

  /// Generate secure hash for data integrity
  static String generateHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Verify data integrity using hash
  static bool verifyHash(String data, String expectedHash) {
    final actualHash = generateHash(data);
    return actualHash == expectedHash;
  }

  /// Generate secure random token
  static String generateSecureToken({int length = 32}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Encrypt sensitive user preferences
  static Future<bool> encryptUserPreferences(Map<String, dynamic> preferences) async {
    try {
      // Only encrypt sensitive fields
      final sensitiveFields = ['email', 'displayName', 'personalNotes'];
      final encryptedPrefs = Map<String, dynamic>.from(preferences);
      
      for (final field in sensitiveFields) {
        if (encryptedPrefs.containsKey(field) && encryptedPrefs[field] is String) {
          final encrypted = await encryptText(encryptedPrefs[field]);
          if (encrypted != null) {
            encryptedPrefs[field] = encrypted;
            encryptedPrefs['${field}_encrypted'] = true;
          }
        }
      }
      
      return await storeSecureJson('user_preferences', encryptedPrefs);
    } catch (e) {
      debugPrint('User preferences encryption error: $e');
      return false;
    }
  }

  /// Decrypt sensitive user preferences
  static Future<Map<String, dynamic>?> decryptUserPreferences() async {
    try {
      final encryptedPrefs = await getSecureJson('user_preferences');
      if (encryptedPrefs == null) return null;
      
      final sensitiveFields = ['email', 'displayName', 'personalNotes'];
      final decryptedPrefs = Map<String, dynamic>.from(encryptedPrefs);
      
      for (final field in sensitiveFields) {
        if (decryptedPrefs['${field}_encrypted'] == true && 
            decryptedPrefs.containsKey(field)) {
          final decrypted = await decryptText(decryptedPrefs[field]);
          if (decrypted != null) {
            decryptedPrefs[field] = decrypted;
          }
          decryptedPrefs.remove('${field}_encrypted');
        }
      }
      
      return decryptedPrefs;
    } catch (e) {
      debugPrint('User preferences decryption error: $e');
      return null;
    }
  }

  /// Clear all encryption keys (for logout/account deletion)
  static Future<void> clearEncryptionKeys() async {
    try {
      await _storage.delete(key: 'encryption_key');
      await _storage.delete(key: 'encryption_iv');
      await _storage.deleteAll();
      
      _encrypter = null;
      _iv = null;
    } catch (e) {
      debugPrint('Encryption keys clearing error: $e');
    }
  }

  /// Rotate encryption keys (for enhanced security)
  static Future<bool> rotateEncryptionKeys() async {
    try {
      // Get all encrypted data first
      final allData = await _storage.readAll();
      final decryptedData = <String, String>{};
      
      // Decrypt all data with old keys
      for (final entry in allData.entries) {
        if (entry.key != 'encryption_key' && entry.key != 'encryption_iv') {
          final decrypted = await decryptText(entry.value);
          if (decrypted != null) {
            decryptedData[entry.key] = decrypted;
          }
        }
      }
      
      // Clear old keys
      await _storage.delete(key: 'encryption_key');
      await _storage.delete(key: 'encryption_iv');
      
      // Generate new keys
      await _initializeEncryption();
      
      // Re-encrypt all data with new keys
      for (final entry in decryptedData.entries) {
        final encrypted = await encryptText(entry.value);
        if (encrypted != null) {
          await _storage.write(key: entry.key, value: encrypted);
        }
      }
      
      return true;
    } catch (e) {
      debugPrint('Key rotation error: $e');
      return false;
    }
  }

  /// Check if encryption is properly initialized
  static bool get isInitialized => _encrypter != null && _iv != null;

  /// Get encryption status for debugging
  static Map<String, dynamic> getEncryptionStatus() {
    return {
      'isInitialized': isInitialized,
      'hasEncrypter': _encrypter != null,
      'hasIV': _iv != null,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
