FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY functions/package*.json ./
RUN npm ci --only=production

# Copy source code
COPY functions/src ./src
COPY functions/lib ./lib

# Set environment
ENV NODE_ENV=production
ENV PORT=8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

EXPOSE 8080

CMD ["npm", "start"]
