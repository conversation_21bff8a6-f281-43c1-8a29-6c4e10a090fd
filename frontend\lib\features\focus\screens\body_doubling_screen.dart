import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:lottie/lottie.dart';

import '../../../shared/widgets/calm_card.dart';
import '../../../shared/widgets/adhd_button.dart';
import '../../../shared/providers/subscription_provider.dart';
import '../providers/body_doubling_provider.dart';
import '../widgets/participant_avatar.dart';
import '../widgets/focus_timer_widget.dart';

class BodyDoublingScreen extends StatefulWidget {
  const BodyDoublingScreen({super.key});

  @override
  State<BodyDoublingScreen> createState() => _BodyDoublingScreenState();
}

class _BodyDoublingScreenState extends State<BodyDoublingScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _pulseController.repeat(reverse: true);
    
    // Initialize body doubling provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BodyDoublingProvider>().initialize();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final subscriptionProvider = context.watch<SubscriptionProvider>();
    
    // Check if user has access to body doubling
    if (!subscriptionProvider.canUseFeature(ProFeature.bodyDoubling)) {
      return _buildUpgradePrompt(context);
    }

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        title: const Text('Body Doubling'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showBodyDoublingInfo(context),
          ),
        ],
      ),
      body: Consumer<BodyDoublingProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return _buildLoadingState(context);
          }

          if (provider.currentRoom == null) {
            return _buildRoomSelection(context, provider);
          }

          return _buildActiveSession(context, provider);
        },
      ),
    );
  }

  Widget _buildUpgradePrompt(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        title: const Text('Body Doubling'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Lottie.asset(
                'assets/lottie/upgrade_prompt.json',
                width: 200,
                height: 200,
              ),
              const SizedBox(height: 24),
              Text(
                'Body Doubling Rooms',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Join virtual co-working sessions with other ADHD users for accountability and focus.',
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ADHDButton(
                text: 'Upgrade to Pro',
                icon: Icons.star,
                type: ADHDButtonType.primary,
                onPressed: () {
                  context.read<SubscriptionProvider>().trackPaywallView();
                  Navigator.pushNamed(context, '/subscription');
                },
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Maybe Later'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Lottie.asset(
            'assets/lottie/loading_meditation.json',
            width: 150,
            height: 150,
          ),
          const SizedBox(height: 24),
          Text(
            'Finding your focus buddy...',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildRoomSelection(BuildContext context, BodyDoublingProvider provider) {
    final theme = Theme.of(context);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CalmCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.people,
                      color: theme.colorScheme.primary,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Body Doubling',
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Work alongside others for accountability',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                ADHDButton(
                  text: 'Quick Join',
                  icon: Icons.flash_on,
                  type: ADHDButtonType.primary,
                  onPressed: () => provider.quickJoinRoom(),
                ),
                const SizedBox(height: 12),
                ADHDButton(
                  text: 'Create Private Room',
                  icon: Icons.add,
                  type: ADHDButtonType.secondary,
                  onPressed: () => _showCreateRoomDialog(context, provider),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Available Rooms',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          ...provider.availableRooms.map((room) => _buildRoomCard(context, room, provider)),
          if (provider.availableRooms.isEmpty)
            CalmCard(
              child: Column(
                children: [
                  Icon(
                    Icons.people_outline,
                    size: 48,
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No active rooms right now',
                    style: theme.textTheme.bodyLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Be the first to start a session!',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRoomCard(BuildContext context, BodyDoublingRoom room, BodyDoublingProvider provider) {
    final theme = Theme.of(context);
    
    return CalmCard(
      child: InkWell(
        onTap: () => provider.joinRoom(room.id),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: room.isActive ? Colors.green : Colors.orange,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      room.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Text(
                    '${room.participants.length}/${room.maxParticipants}',
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                room.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  ...room.participants.take(3).map((participant) => 
                    Padding(
                      padding: const EdgeInsets.only(right: 4),
                      child: ParticipantAvatar(
                        participant: participant,
                        size: 24,
                      ),
                    ),
                  ),
                  if (room.participants.length > 3)
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '+${room.participants.length - 3}',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActiveSession(BuildContext context, BodyDoublingProvider provider) {
    final theme = Theme.of(context);
    final room = provider.currentRoom!;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Room info
          CalmCard(
            backgroundColor: theme.colorScheme.primaryContainer.withOpacity(0.3),
            child: Column(
              children: [
                Row(
                  children: [
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _pulseAnimation.value,
                          child: Container(
                            width: 16,
                            height: 16,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            room.name,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            '${room.participants.length} people focusing',
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.exit_to_app),
                      onPressed: () => provider.leaveRoom(),
                      tooltip: 'Leave Room',
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          
          // Focus timer
          const FocusTimerWidget(),
          const SizedBox(height: 24),
          
          // Participants
          CalmCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Focus Buddies',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children: room.participants.map((participant) => 
                    ParticipantAvatar(
                      participant: participant,
                      size: 48,
                      showStatus: true,
                    ),
                  ).toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showBodyDoublingInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('What is Body Doubling?'),
        content: const Text(
          'Body doubling is working alongside others to increase focus and accountability. '
          'You\'ll work on your own tasks while others do the same, creating a supportive environment '
          'that helps with ADHD focus challenges.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }

  void _showCreateRoomDialog(BuildContext context, BodyDoublingProvider provider) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Room'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Room Name',
                hintText: 'e.g., Morning Focus Session',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'What are you working on?',
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isNotEmpty) {
                provider.createRoom(
                  nameController.text,
                  descriptionController.text,
                );
                Navigator.pop(context);
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }
}
