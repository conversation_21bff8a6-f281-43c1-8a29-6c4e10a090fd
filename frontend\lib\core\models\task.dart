import 'package:cloud_firestore/cloud_firestore.dart';

class Task {
  final String id;
  final String userId;
  final String title;
  final String? description;
  final TaskPriority priority;
  final int estimatedMinutes;
  final int actualMinutes;
  final TaskStatus status;
  final AIBreakdown? aiBreakdown;
  final List<String> tags;
  final DateTime? dueDate;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? parentTaskId;
  final bool isSubtask;

  Task({
    required this.id,
    required this.userId,
    required this.title,
    this.description,
    required this.priority,
    required this.estimatedMinutes,
    this.actualMinutes = 0,
    required this.status,
    this.aiBreakdown,
    this.tags = const [],
    this.dueDate,
    required this.createdAt,
    this.completedAt,
    this.parentTaskId,
    this.isSubtask = false,
  });

  factory Task.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return Task(
      id: doc.id,
      userId: data['userId'] ?? '',
      title: data['title'] ?? '',
      description: data['description'],
      priority: TaskPriority.values.firstWhere(
        (e) => e.index == (data['priority'] ?? 2) - 1,
        orElse: () => TaskPriority.medium,
      ),
      estimatedMinutes: data['estimatedMinutes'] ?? 30,
      actualMinutes: data['actualMinutes'] ?? 0,
      status: TaskStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => TaskStatus.todo,
      ),
      aiBreakdown: data['aiBreakdown'] != null 
          ? AIBreakdown.fromMap(data['aiBreakdown'])
          : null,
      tags: List<String>.from(data['tags'] ?? []),
      dueDate: data['dueDate'] != null 
          ? (data['dueDate'] as Timestamp).toDate()
          : null,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      completedAt: data['completedAt'] != null 
          ? (data['completedAt'] as Timestamp).toDate()
          : null,
      parentTaskId: data['parentTaskId'],
      isSubtask: data['isSubtask'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'title': title,
      'description': description,
      'priority': priority.index + 1,
      'estimatedMinutes': estimatedMinutes,
      'actualMinutes': actualMinutes,
      'status': status.name,
      'aiBreakdown': aiBreakdown?.toMap(),
      'tags': tags,
      'dueDate': dueDate != null ? Timestamp.fromDate(dueDate!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'parentTaskId': parentTaskId,
      'isSubtask': isSubtask,
    };
  }

  Task copyWith({
    String? title,
    String? description,
    TaskPriority? priority,
    int? estimatedMinutes,
    int? actualMinutes,
    TaskStatus? status,
    AIBreakdown? aiBreakdown,
    List<String>? tags,
    DateTime? dueDate,
    DateTime? completedAt,
    String? parentTaskId,
    bool? isSubtask,
  }) {
    return Task(
      id: id,
      userId: userId,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      estimatedMinutes: estimatedMinutes ?? this.estimatedMinutes,
      actualMinutes: actualMinutes ?? this.actualMinutes,
      status: status ?? this.status,
      aiBreakdown: aiBreakdown ?? this.aiBreakdown,
      tags: tags ?? this.tags,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt,
      completedAt: completedAt ?? this.completedAt,
      parentTaskId: parentTaskId ?? this.parentTaskId,
      isSubtask: isSubtask ?? this.isSubtask,
    );
  }

  bool get isCompleted => status == TaskStatus.completed;
  bool get isInProgress => status == TaskStatus.inProgress;
  bool get isOverdue => dueDate != null && 
      dueDate!.isBefore(DateTime.now()) && 
      !isCompleted;

  String get priorityLabel {
    switch (priority) {
      case TaskPriority.high:
        return 'High';
      case TaskPriority.medium:
        return 'Medium';
      case TaskPriority.low:
        return 'Low';
    }
  }

  String get estimatedTimeLabel {
    if (estimatedMinutes < 60) {
      return '${estimatedMinutes}m';
    } else {
      final hours = estimatedMinutes ~/ 60;
      final minutes = estimatedMinutes % 60;
      return minutes > 0 ? '${hours}h ${minutes}m' : '${hours}h';
    }
  }
}

enum TaskPriority { high, medium, low }
enum TaskStatus { todo, inProgress, completed, archived }

class AIBreakdown {
  final List<String> steps;
  final int difficulty;
  final List<int> suggestedOrder;
  final List<int> timeEstimates;
  final List<String> tips;
  final DateTime cachedAt;
  final String model;

  AIBreakdown({
    required this.steps,
    required this.difficulty,
    required this.suggestedOrder,
    required this.timeEstimates,
    required this.tips,
    required this.cachedAt,
    required this.model,
  });

  factory AIBreakdown.fromMap(Map<String, dynamic> map) {
    return AIBreakdown(
      steps: List<String>.from(map['steps'] ?? []),
      difficulty: map['difficulty'] ?? 3,
      suggestedOrder: List<int>.from(map['suggestedOrder'] ?? []),
      timeEstimates: List<int>.from(map['timeEstimates'] ?? []),
      tips: List<String>.from(map['tips'] ?? []),
      cachedAt: (map['cachedAt'] as Timestamp).toDate(),
      model: map['model'] ?? 'unknown',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'steps': steps,
      'difficulty': difficulty,
      'suggestedOrder': suggestedOrder,
      'timeEstimates': timeEstimates,
      'tips': tips,
      'cachedAt': Timestamp.fromDate(cachedAt),
      'model': model,
    };
  }

  String get difficultyLabel {
    switch (difficulty) {
      case 1:
        return 'Very Easy';
      case 2:
        return 'Easy';
      case 3:
        return 'Medium';
      case 4:
        return 'Hard';
      case 5:
        return 'Very Hard';
      default:
        return 'Medium';
    }
  }

  int get totalEstimatedMinutes => timeEstimates.fold(0, (sum, time) => sum + time);
}
