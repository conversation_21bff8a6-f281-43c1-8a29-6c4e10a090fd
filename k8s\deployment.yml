apiVersion: apps/v1
kind: Deployment
metadata:
  name: focusflow-api
  labels:
    app: focusflow-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: focusflow-api
  template:
    metadata:
      labels:
        app: focusflow-api
    spec:
      containers:
      - name: focusflow-api
        image: gcr.io/focusflow-prod/api:latest
        ports:
        - containerPort: 8080
        env:
        - name: NODE_ENV
          value: "production"
        - name: FIREBASE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: focusflow-secrets
              key: firebase-project-id
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: focusflow-secrets
              key: openai-api-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: focusflow-api-service
spec:
  selector:
    app: focusflow-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
