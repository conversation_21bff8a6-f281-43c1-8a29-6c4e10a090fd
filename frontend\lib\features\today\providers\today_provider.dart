import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../core/models/task.dart';
import '../../../core/models/user.dart';
import '../../../core/services/firebase_service.dart';
import '../../../core/services/ai_service.dart';

class TodayProvider extends ChangeNotifier {
  List<Task> _todaysThree = [];
  int _energyLevel = 0;
  int _completedTasksToday = 0;
  int _currentStreak = 0;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Task> get todaysThree => _todaysThree;
  int get energyLevel => _energyLevel;
  int get completedTasksToday => _completedTasksToday;
  int get currentStreak => _currentStreak;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load today's data
  Future<void> loadTodaysData() async {
    _setLoading(true);
    try {
      final user = FirebaseService.auth.currentUser;
      if (user == null) return;
      
      final today = DateTime.now().toIso8601String().split('T')[0];
      
      // Load tasks for today
      final tasksQuery = await FirebaseService.firestore
          .collection('tasks')
          .where('userId', isEqualTo: user.uid)
          .where('dueDate', isGreaterThanOrEqualTo: Timestamp.fromDate(DateTime.now().subtract(const Duration(days: 1))))
          .where('dueDate', isLessThan: Timestamp.fromDate(DateTime.now().add(const Duration(days: 1))))
          .orderBy('dueDate')
          .limit(3)
          .get();
      
      _todaysThree = tasksQuery.docs.map((doc) => Task.fromFirestore(doc)).toList();
      _completedTasksToday = _todaysThree.where((t) => t.isCompleted).length;
      
      // Load user stats
      final userDoc = await FirebaseService.firestore
          .collection('users')
          .doc(user.uid)
          .get();
      
      if (userDoc.exists) {
        final userData = User.fromFirestore(userDoc);
        _currentStreak = userData.stats.currentStreak;
      }
      
      _error = null;
    } catch (e) {
      _error = 'Failed to load today\'s data: $e';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateEnergyLevel(int level) async {
    _energyLevel = level;
    notifyListeners();
    
    try {
      final user = FirebaseService.auth.currentUser;
      if (user == null) return;
      
      // Log energy level for analytics
      await FirebaseService.logEvent('energy_level_updated', {
        'level': level,
        'time_of_day': DateTime.now().hour,
      });
      
      // Update user's daily energy tracking
      await FirebaseService.firestore
          .collection('daily_energy')
          .doc('${user.uid}_${DateTime.now().toIso8601String().split('T')[0]}')
          .set({
        'userId': user.uid,
        'date': DateTime.now().toIso8601String().split('T')[0],
        'energyLevel': level,
        'timestamp': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    } catch (e) {
      debugPrint('Failed to update energy level: $e');
    }
  }

  // Add task to today's three
  Future<void> addToTodaysThree(Task task) async {
    if (_todaysThree.length >= 3) {
      _error = 'You can only have 3 tasks for today. Keep it simple!';
      notifyListeners();
      return;
    }
    
    _todaysThree.add(task);
    notifyListeners();
    
    try {
      // Create the task in Firestore
      await FirebaseService.firestore
          .collection('tasks')
          .doc(task.id)
          .set(task.toFirestore());
      
      await FirebaseService.logEvent('task_added_to_today', {
        'task_count': _todaysThree.length,
      });
    } catch (e) {
      debugPrint('Failed to add task to today\'s three: $e');
    }
  }

  Future<void> completeTask(String taskId) async {
    final taskIndex = _todaysThree.indexWhere((t) => t.id == taskId);
    if (taskIndex == -1) return;
    
    final task = _todaysThree[taskIndex];
    final completedTask = task.copyWith(
      status: TaskStatus.completed,
      completedAt: DateTime.now(),
    );
    
    _todaysThree[taskIndex] = completedTask;
    _completedTasksToday++;
    notifyListeners();
    
    try {
      // Update task in Firestore
      await FirebaseService.firestore
          .collection('tasks')
          .doc(taskId)
          .update({
        'status': TaskStatus.completed.name,
        'completedAt': FieldValue.serverTimestamp(),
      });
      
      // Update user stats
      final user = FirebaseService.auth.currentUser;
      if (user != null) {
        await _updateUserStats(user.uid);
      }
      
      // Log completion event
      await FirebaseService.logEvent('task_completed', {
        'task_priority': task.priority.name,
        'estimated_minutes': task.estimatedMinutes,
        'completion_time': DateTime.now().hour,
      });
      
    } catch (e) {
      debugPrint('Failed to complete task: $e');
    }
  }

  // Reorder today's three
  Future<void> reorderTodaysThree(int oldIndex, int newIndex) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    
    final task = _todaysThree.removeAt(oldIndex);
    _todaysThree.insert(newIndex, task);
    notifyListeners();
  }

  Future<void> generateTaskBreakdown(String taskId) async {
    try {
      final taskIndex = _todaysThree.indexWhere((t) => t.id == taskId);
      if (taskIndex == -1) return;
      
      final task = _todaysThree[taskIndex];
      
      // Generate AI breakdown using the AI service
      final breakdown = await AIService.getTaskBreakdown(task.title, task.description);
      
      if (breakdown != null) {
        final updatedTask = task.copyWith(aiBreakdown: breakdown);
        _todaysThree[taskIndex] = updatedTask;
        
        // Update task in Firestore
        await FirebaseService.firestore
            .collection('tasks')
            .doc(taskId)
            .update({'aiBreakdown': breakdown.toMap()});
        
        notifyListeners();
        
        await FirebaseService.logEvent('ai_breakdown_generated', {
          'task_difficulty': breakdown.difficulty,
          'steps_count': breakdown.steps.length,
        });
      }
    } catch (e) {
      _error = 'Failed to generate task breakdown: $e';
      debugPrint(_error);
      notifyListeners();
    }
  }

  Future<void> generateAdaptivePlan() async {
    if (_energyLevel == 0) return;
    
    try {
      // Sort tasks based on energy level and priority
      _todaysThree.sort((a, b) {
        if (_energyLevel >= 4) {
          // High energy: prioritize difficult tasks first
          return b.priority.index.compareTo(a.priority.index);
        } else {
          // Low energy: prioritize easier tasks first
          return a.priority.index.compareTo(b.priority.index);
        }
      });
      
      notifyListeners();
      
      await FirebaseService.logEvent('adaptive_plan_generated', {
        'energy_level': _energyLevel,
        'task_count': _todaysThree.length,
      });
    } catch (e) {
      debugPrint('Failed to generate adaptive plan: $e');
    }
  }

  Future<void> _updateUserStats(String userId) async {
    try {
      final userRef = FirebaseService.firestore.collection('users').doc(userId);
      
      await FirebaseService.firestore.runTransaction((transaction) async {
        final userDoc = await transaction.get(userRef);
        if (!userDoc.exists) return;
        
        final userData = User.fromFirestore(userDoc);
        final today = DateTime.now().toIso8601String().split('T')[0];
        
        // Update streak
        int newStreak = userData.stats.currentStreak;
        if (userData.stats.lastActiveDate != today) {
          final lastActive = DateTime.parse(userData.stats.lastActiveDate);
          final daysDiff = DateTime.now().difference(lastActive).inDays;
          
          if (daysDiff == 1) {
            newStreak += 1; // Continue streak
          } else if (daysDiff > 1) {
            newStreak = 1; // Reset streak
          }
        }
        
        final updatedStats = userData.stats.copyWith(
          totalTasksCompleted: userData.stats.totalTasksCompleted + 1,
          currentStreak: newStreak,
          longestStreak: newStreak > userData.stats.longestStreak 
              ? newStreak 
              : userData.stats.longestStreak,
          lastActiveDate: today,
        );
        
        transaction.update(userRef, {'stats': updatedStats.toMap()});
        _currentStreak = newStreak;
      });
    } catch (e) {
      debugPrint('Failed to update user stats: $e');
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
