import { onCall, onSchedule } from "firebase-functions/v2/https"
import { onDocumentUpdated } from "firebase-functions/v2/firestore"
import { initializeApp } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"
import { AIService } from "./services/aiService"
import type { Task, User, DailyPlan } from "./models/types"

initializeApp()
const db = getFirestore()

/**
 * Generate AI task breakdown when a new task is created
 */
export const generateTaskBreakdown = onCall(async (request) => {
  const { taskId } = request.data
  const userId = request.auth?.uid

  if (!userId) {
    throw new Error("Authentication required")
  }

  try {
    // Get task and user data
    const [taskDoc, userDoc] = await Promise.all([
      db.collection("tasks").doc(taskId).get(),
      db.collection("users").doc(userId).get(),
    ])

    if (!taskDoc.exists || !userDoc.exists) {
      throw new Error("Task or user not found")
    }

    const task = taskDoc.data() as Task
    const user = userDoc.data() as User

    // Verify ownership
    if (task.userId !== userId) {
      throw new Error("Unauthorized")
    }

    // Check subscription limits for free users
    if (user.subscription.tier === "free") {
      const today = new Date().toISOString().split("T")[0]
      const todayBreakdowns = await db
        .collection("tasks")
        .where("userId", "==", userId)
        .where("createdAt", ">=", new Date(today))
        .where("aiBreakdown", "!=", null)
        .get()

      if (todayBreakdowns.size >= 5) {
        throw new Error("Daily AI breakdown limit reached. Upgrade to Pro for unlimited breakdowns.")
      }
    }

    // Generate AI breakdown
    const aiBreakdown = await AIService.generateTaskBreakdown(task, user)

    // Update task with breakdown
    await taskDoc.ref.update({
      aiBreakdown,
      updatedAt: new Date(),
    })

    return { success: true, breakdown: aiBreakdown }
  } catch (error) {
    console.error("Task breakdown generation failed:", error)
    throw error
  }
})

/**
 * Generate adaptive daily plan based on user's energy and habits
 */
export const generateDailyPlan = onCall(async (request) => {
  const { date, energyLevel, availableMinutes } = request.data
  const userId = request.auth?.uid

  if (!userId) {
    throw new Error("Authentication required")
  }

  try {
    const userDoc = await db.collection("users").doc(userId).get()
    if (!userDoc.exists) {
      throw new Error("User not found")
    }

    const user = userDoc.data() as User

    // Get pending tasks
    const tasksQuery = await db
      .collection("tasks")
      .where("userId", "==", userId)
      .where("status", "in", ["todo", "in-progress"])
      .orderBy("priority")
      .orderBy("createdAt")
      .limit(20)
      .get()

    const tasks = tasksQuery.docs.map((doc) => ({ id: doc.id, ...doc.data() })) as Task[]

    // AI-powered task selection and ordering
    const aiSuggestions = await this.generateAdaptivePlan(tasks, user, energyLevel, availableMinutes)

    // Create or update daily plan
    const planRef = db.collection("dailyPlans").doc(userId).collection("plans").doc(date)
    const planData: Partial<DailyPlan> = {
      userId,
      date,
      todaysThree: aiSuggestions.taskOrder.slice(0, 3),
      energyLevel,
      aiSuggestions,
      updatedAt: new Date(),
    }

    await planRef.set(planData, { merge: true })

    return { success: true, plan: planData }
  } catch (error) {
    console.error("Daily plan generation failed:", error)
    throw error
  }
})

/**
 * Generate weekly reflection with AI insights
 */
export const generateWeeklyReflection = onCall(async (request) => {
  const { weekStart } = request.data
  const userId = request.auth?.uid

  if (!userId) {
    throw new Error("Authentication required")
  }

  try {
    const weekEnd = new Date(weekStart)
    weekEnd.setDate(weekEnd.getDate() + 6)

    // Gather week's data
    const [completedTasks, focusSessions, dailyPlans] = await Promise.all([
      db
        .collection("tasks")
        .where("userId", "==", userId)
        .where("status", "==", "completed")
        .where("completedAt", ">=", new Date(weekStart))
        .where("completedAt", "<=", weekEnd)
        .get(),
      db
        .collection("focusSessions")
        .where("userId", "==", userId)
        .where("startedAt", ">=", new Date(weekStart))
        .where("startedAt", "<=", weekEnd)
        .get(),
      db
        .collection("dailyPlans")
        .doc(userId)
        .collection("plans")
        .where("date", ">=", weekStart)
        .where("date", "<=", weekEnd.toISOString().split("T")[0])
        .get(),
    ])

    const weekData = {
      completedTasks: completedTasks.docs.map((doc) => doc.data()),
      focusSessions: focusSessions.docs.map((doc) => doc.data()),
      dailyPlans: dailyPlans.docs.map((doc) => doc.data()),
    }

    // Generate AI analysis
    const aiAnalysis = await AIService.generateWeeklyReflection(userId, weekData)

    // Save reflection
    const reflectionId = `${weekStart.split("-")[0]}-W${Math.ceil(new Date(weekStart).getDate() / 7)}`
    const reflectionRef = db.collection("weeklyReflections").doc(`${userId}_${reflectionId}`)

    await reflectionRef.set({
      id: reflectionId,
      userId,
      weekStart,
      weekEnd: weekEnd.toISOString().split("T")[0],
      aiAnalysis,
      createdAt: new Date(),
    })

    return { success: true, analysis: aiAnalysis }
  } catch (error) {
    console.error("Weekly reflection generation failed:", error)
    throw error
  }
})

/**
 * Send smart nudges based on user behavior
 */
export const sendSmartNudges = onSchedule("every 2 hours", async (event) => {
  const now = new Date()
  const currentHour = now.getHours()

  // Only send nudges during reasonable hours (8 AM - 8 PM)
  if (currentHour < 8 || currentHour > 20) {
    return
  }

  try {
    // Get users who have notifications enabled and haven't been active recently
    const usersQuery = await db
      .collection("users")
      .where("preferences.notifications", "==", true)
      .where("stats.lastActiveDate", "<", now.toISOString().split("T")[0])
      .limit(100) // Process in batches
      .get()

    const nudgePromises = usersQuery.docs.map(async (userDoc) => {
      const user = userDoc.data() as User

      // Generate contextual nudge
      const nudge = await AIService.generateSmartNudge(user, {
        timeOfDay: currentHour,
        lastActivity: user.stats.lastActiveDate,
        currentStreak: user.stats.currentStreak,
      })

      // Send notification (implement with FCM)
      // await sendPushNotification(user.id, nudge);

      console.log(`Nudge for ${user.id}: ${nudge}`)
    })

    await Promise.all(nudgePromises)
  } catch (error) {
    console.error("Smart nudges failed:", error)
  }
})

/**
 * Update user stats when tasks are completed
 */
export const updateUserStats = onDocumentUpdated("tasks/{taskId}", async (event) => {
  const before = event.data?.before.data()
  const after = event.data?.after.data()

  if (!before || !after) return

  // Check if task was just completed
  if (before.status !== "completed" && after.status === "completed") {
    const userId = after.userId
    const userRef = db.collection("users").doc(userId)

    try {
      await db.runTransaction(async (transaction) => {
        const userDoc = await transaction.get(userRef)
        if (!userDoc.exists) return

        const user = userDoc.data() as User
        const today = new Date().toISOString().split("T")[0]
        const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split("T")[0]

        // Update streak
        let newStreak = user.stats.currentStreak
        if (user.stats.lastActiveDate === yesterday) {
          newStreak += 1
        } else if (user.stats.lastActiveDate !== today) {
          newStreak = 1
        }

        // Update stats
        transaction.update(userRef, {
          "stats.totalTasksCompleted": user.stats.totalTasksCompleted + 1,
          "stats.currentStreak": newStreak,
          "stats.longestStreak": Math.max(user.stats.longestStreak, newStreak),
          "stats.lastActiveDate": today,
        })
      })
    } catch (error) {
      console.error("User stats update failed:", error)
    }
  }
})

// Helper function for adaptive planning (simplified version)
async function generateAdaptivePlan(
  tasks: Task[],
  user: User,
  energyLevel: number,
  availableMinutes: number,
): Promise<DailyPlan["aiSuggestions"]> {
  // Simple algorithm - in production, this would use more sophisticated AI
  const sortedTasks = tasks
    .filter((task) => task.estimatedMinutes <= availableMinutes)
    .sort((a, b) => {
      // Prioritize by: priority, then energy match, then estimated time
      if (a.priority !== b.priority) return a.priority - b.priority

      // Match high-energy tasks with high energy levels
      const aEnergyMatch = energyLevel >= 4 ? (a.estimatedMinutes > 30 ? 1 : 0) : a.estimatedMinutes <= 30 ? 1 : 0
      const bEnergyMatch = energyLevel >= 4 ? (b.estimatedMinutes > 30 ? 1 : 0) : b.estimatedMinutes <= 30 ? 1 : 0

      if (aEnergyMatch !== bEnergyMatch) return bEnergyMatch - aEnergyMatch

      return a.estimatedMinutes - b.estimatedMinutes
    })

  return {
    taskOrder: sortedTasks.slice(0, 5).map((t) => t.id),
    energyOptimized: true,
    reasoning: `Selected tasks based on your ${energyLevel}/5 energy level and ${availableMinutes} available minutes.`,
    generatedAt: new Date(),
  }
}
