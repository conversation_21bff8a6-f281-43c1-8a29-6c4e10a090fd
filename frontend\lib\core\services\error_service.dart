import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'analytics_service.dart';

class ErrorService {
  static final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;
  static final List<AppError> _errorHistory = [];
  static StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  static bool _isOnline = true;

  /// Initialize error handling
  static Future<void> initialize() async {
    try {
      // Set up Crashlytics
      await _crashlytics.setCrashlyticsCollectionEnabled(!kDebugMode);
      
      // Handle Flutter framework errors
      FlutterError.onError = (FlutterErrorDetails details) {
        _handleFlutterError(details);
      };
      
      // Handle errors outside of Flutter framework
      PlatformDispatcher.instance.onError = (error, stack) {
        _handlePlatformError(error, stack);
        return true;
      };
      
      // Handle isolate errors
      Isolate.current.addErrorListener(RawReceivePort((pair) async {
        final List<dynamic> errorAndStacktrace = pair;
        await _handleIsolateError(
          errorAndStacktrace.first,
          errorAndStacktrace.last,
        );
      }).sendPort);
      
      // Monitor connectivity
      _monitorConnectivity();
      
      debugPrint('Error handling initialized');
    } catch (e) {
      debugPrint('Error service initialization failed: $e');
    }
  }

  /// Handle Flutter framework errors
  static void _handleFlutterError(FlutterErrorDetails details) {
    final error = AppError(
      type: ErrorType.flutter,
      message: details.exception.toString(),
      stackTrace: details.stack.toString(),
      context: details.context?.toString(),
      timestamp: DateTime.now(),
      isFatal: false,
    );
    
    _recordError(error);
    
    // Report to Crashlytics
    _crashlytics.recordFlutterFatalError(details);
    
    // In debug mode, also print to console
    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  }

  /// Handle platform errors
  static bool _handlePlatformError(Object error, StackTrace stack) {
    final appError = AppError(
      type: ErrorType.platform,
      message: error.toString(),
      stackTrace: stack.toString(),
      timestamp: DateTime.now(),
      isFatal: true,
    );
    
    _recordError(appError);
    
    // Report to Crashlytics
    _crashlytics.recordError(error, stack, fatal: true);
    
    return true;
  }

  /// Handle isolate errors
  static Future<void> _handleIsolateError(dynamic error, dynamic stackTrace) async {
    final appError = AppError(
      type: ErrorType.isolate,
      message: error.toString(),
      stackTrace: stackTrace.toString(),
      timestamp: DateTime.now(),
      isFatal: true,
    );
    
    _recordError(appError);
    
    // Report to Crashlytics
    await _crashlytics.recordError(error, stackTrace, fatal: true);
  }

  /// Record error in local history and analytics
  static void _recordError(AppError error) {
    _errorHistory.add(error);
    
    // Keep only last 100 errors to prevent memory issues
    if (_errorHistory.length > 100) {
      _errorHistory.removeAt(0);
    }
    
    // Log to analytics
    AnalyticsService.logError(
      error: error.message,
      context: error.context ?? error.type.name,
      stackTrace: StackTrace.fromString(error.stackTrace),
      additionalData: {
        'error_type': error.type.name,
        'is_fatal': error.isFatal,
        'is_online': _isOnline,
        'timestamp': error.timestamp.toIso8601String(),
      },
    );
  }

  /// Monitor connectivity for error context
  static void _monitorConnectivity() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (ConnectivityResult result) {
        _isOnline = result != ConnectivityResult.none;
      },
    );
  }

  /// Handle API errors with retry logic
  static Future<T> handleApiCall<T>(
    Future<T> Function() apiCall, {
    String? context,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await apiCall();
      } catch (e, stack) {
        attempts++;
        
        final error = AppError(
          type: ErrorType.api,
          message: e.toString(),
          stackTrace: stack.toString(),
          context: context,
          timestamp: DateTime.now(),
          isFatal: false,
          retryAttempt: attempts,
        );
        
        _recordError(error);
        
        // If this was the last attempt, rethrow
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        // Wait before retrying
        await Future.delayed(retryDelay * attempts);
      }
    }
    
    throw Exception('Max retries exceeded');
  }

  /// Handle database errors with fallback
  static Future<T?> handleDatabaseOperation<T>(
    Future<T> Function() operation, {
    String? context,
    T? fallbackValue,
  }) async {
    try {
      return await operation();
    } catch (e, stack) {
      final error = AppError(
        type: ErrorType.database,
        message: e.toString(),
        stackTrace: stack.toString(),
        context: context,
        timestamp: DateTime.now(),
        isFatal: false,
      );
      
      _recordError(error);
      
      // Return fallback value if provided
      return fallbackValue;
    }
  }

  /// Show user-friendly error message
  static void showUserError(
    BuildContext context,
    String message, {
    String? title,
    VoidCallback? onRetry,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title ?? 'Oops!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: 16),
            Text(
              'Don\'t worry, this happens sometimes with ADHD apps. We\'re here to help! 🧠💪',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
        actions: [
          if (onRetry != null)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                onRetry();
              },
              child: const Text('Try Again'),
            ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show network error with retry option
  static void showNetworkError(
    BuildContext context, {
    VoidCallback? onRetry,
  }) {
    showUserError(
      context,
      'It looks like you\'re offline or having connection issues. Check your internet connection and try again.',
      title: 'Connection Problem',
      onRetry: onRetry,
    );
  }

  /// Get error statistics
  static Map<String, dynamic> getErrorStatistics() {
    final now = DateTime.now();
    final last24Hours = _errorHistory
        .where((error) => now.difference(error.timestamp).inHours < 24)
        .toList();
    
    final errorsByType = <ErrorType, int>{};
    final fatalErrors = <AppError>[];
    
    for (final error in last24Hours) {
      errorsByType[error.type] = (errorsByType[error.type] ?? 0) + 1;
      if (error.isFatal) {
        fatalErrors.add(error);
      }
    }
    
    return {
      'total_errors_24h': last24Hours.length,
      'fatal_errors_24h': fatalErrors.length,
      'errors_by_type': errorsByType.map((k, v) => MapEntry(k.name, v)),
      'most_recent_error': _errorHistory.isNotEmpty 
          ? _errorHistory.last.toJson() 
          : null,
      'is_online': _isOnline,
    };
  }

  /// Clear error history
  static void clearErrorHistory() {
    _errorHistory.clear();
  }

  /// Set user identifier for crash reporting
  static Future<void> setUserIdentifier(String userId) async {
    await _crashlytics.setUserIdentifier(userId);
  }

  /// Set custom key for crash reporting
  static Future<void> setCustomKey(String key, String value) async {
    await _crashlytics.setCustomKey(key, value);
  }

  /// Log custom message to crash reporting
  static void log(String message) {
    _crashlytics.log(message);
  }

  /// Dispose resources
  static void dispose() {
    _connectivitySubscription?.cancel();
  }
}

enum ErrorType {
  flutter,
  platform,
  isolate,
  api,
  database,
  user,
}

class AppError {
  final ErrorType type;
  final String message;
  final String stackTrace;
  final String? context;
  final DateTime timestamp;
  final bool isFatal;
  final int? retryAttempt;

  AppError({
    required this.type,
    required this.message,
    required this.stackTrace,
    this.context,
    required this.timestamp,
    required this.isFatal,
    this.retryAttempt,
  });

  Map<String, dynamic> toJson() => {
    'type': type.name,
    'message': message,
    'stackTrace': stackTrace,
    'context': context,
    'timestamp': timestamp.toIso8601String(),
    'isFatal': isFatal,
    'retryAttempt': retryAttempt,
  };
}

/// Custom error widget for better user experience
class ADHDErrorWidget extends StatelessWidget {
  final FlutterErrorDetails errorDetails;
  final VoidCallback? onRetry;

  const ADHDErrorWidget({
    super.key,
    required this.errorDetails,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.psychology,
              size: 64,
              color: theme.colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 24),
            Text(
              'Oops! Something went wrong',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Don\'t worry, this happens sometimes. Your ADHD brain is still amazing! 🧠✨',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            if (onRetry != null)
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            const SizedBox(height: 16),
            if (kDebugMode)
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    errorDetails.toString(),
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontFamily: 'monospace',
                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
