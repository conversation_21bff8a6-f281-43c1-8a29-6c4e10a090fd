import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  
  static Future<void> initialize() async {
    try {
      // Initialize timezone data
      tz.initializeTimeZones();
      
      // Initialize local notifications
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );
      
      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );
      
      await _notifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );
      
      // Create notification channels for Android
      await _createNotificationChannels();
      
      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
      
      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      
      // Handle notification taps when app is in background
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
      
      print('Notification service initialized successfully');
    } catch (e) {
      print('Notification service initialization error: $e');
    }
  }
  
  static Future<void> _createNotificationChannels() async {
    // ADHD-friendly notification channels with gentle sounds
    const channels = [
      AndroidNotificationChannel(
        'focus_reminders',
        'Focus Reminders',
        description: 'Gentle reminders for focus sessions',
        importance: Importance.defaultImportance,
        sound: RawResourceAndroidNotificationSound('gentle_chime'),
      ),
      AndroidNotificationChannel(
        'task_nudges',
        'Task Nudges',
        description: 'Gentle nudges for pending tasks',
        importance: Importance.low,
        sound: RawResourceAndroidNotificationSound('soft_bell'),
      ),
      AndroidNotificationChannel(
        'celebrations',
        'Celebrations',
        description: 'Celebration notifications for completed tasks',
        importance: Importance.high,
        sound: RawResourceAndroidNotificationSound('success_chime'),
      ),
    ];
    
    for (final channel in channels) {
      await _notifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }
  }
  
  // Schedule ADHD-friendly focus reminders
  static Future<void> scheduleFocusReminder({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
  }) async {
    try {
      await _notifications.zonedSchedule(
        id,
        title,
        body,
        tz.TZDateTime.from(scheduledTime, tz.local),
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'focus_reminders',
            'Focus Reminders',
            channelDescription: 'Gentle reminders for focus sessions',
            importance: Importance.defaultImportance,
            priority: Priority.defaultPriority,
            sound: RawResourceAndroidNotificationSound('gentle_chime'),
            enableVibration: true,
            vibrationPattern: Int64List.fromList([0, 250, 250, 250]), // Gentle vibration
          ),
          iOS: DarwinNotificationDetails(
            sound: 'gentle_chime.aiff',
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      );
    } catch (e) {
      print('Schedule focus reminder error: $e');
    }
  }
  
  // Schedule gentle task nudges (not overwhelming)
  static Future<void> scheduleTaskNudge({
    required int id,
    required String taskTitle,
    required DateTime scheduledTime,
  }) async {
    try {
      await _notifications.zonedSchedule(
        id,
        'Gentle reminder 🌱',
        'When you\'re ready: $taskTitle',
        tz.TZDateTime.from(scheduledTime, tz.local),
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'task_nudges',
            'Task Nudges',
            channelDescription: 'Gentle nudges for pending tasks',
            importance: Importance.low,
            priority: Priority.low,
            sound: RawResourceAndroidNotificationSound('soft_bell'),
            enableVibration: false, // No vibration for gentle nudges
          ),
          iOS: DarwinNotificationDetails(
            sound: 'soft_bell.aiff',
            presentAlert: true,
            presentBadge: false,
            presentSound: true,
          ),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      );
    } catch (e) {
      print('Schedule task nudge error: $e');
    }
  }
  
  // Show celebration notification
  static Future<void> showCelebration({
    required String message,
    String? taskTitle,
  }) async {
    try {
      await _notifications.show(
        DateTime.now().millisecondsSinceEpoch ~/ 1000,
        '🎉 Awesome work!',
        message,
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'celebrations',
            'Celebrations',
            channelDescription: 'Celebration notifications for completed tasks',
            importance: Importance.high,
            priority: Priority.high,
            sound: RawResourceAndroidNotificationSound('success_chime'),
            enableVibration: true,
            vibrationPattern: Int64List.fromList([0, 100, 100, 100, 100, 100]), // Happy vibration
          ),
          iOS: DarwinNotificationDetails(
            sound: 'success_chime.aiff',
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
      );
    } catch (e) {
      print('Show celebration error: $e');
    }
  }
  
  // Cancel specific notification
  static Future<void> cancelNotification(int id) async {
    try {
      await _notifications.cancel(id);
    } catch (e) {
      print('Cancel notification error: $e');
    }
  }
  
  // Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    try {
      await _notifications.cancelAll();
    } catch (e) {
      print('Cancel all notifications error: $e');
    }
  }
  
  static void _onNotificationTapped(NotificationResponse response) {
    print('Notification tapped: ${response.payload}');
    // Handle notification tap - navigate to appropriate screen
  }
  
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('Foreground message: ${message.notification?.title}');
    // Handle foreground FCM messages
  }
  
  static void _handleNotificationTap(RemoteMessage message) {
    print('Notification opened app: ${message.notification?.title}');
    // Handle notification tap that opened the app
  }
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('Background message: ${message.notification?.title}');
  // Handle background FCM messages
}
